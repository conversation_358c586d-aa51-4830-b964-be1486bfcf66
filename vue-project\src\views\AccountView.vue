<template>
  <div class="account-view">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">我的账户</h1>
        <p class="page-description">管理您的订阅和订单</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="userStatsStore.isLoading && !hasAnyData" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载账户信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="userStatsStore.error && !hasAnyData" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ userStatsStore.error }}</p>
        <button class="btn btn-primary" @click="loadData">
          重试
        </button>
      </div>

      <!-- 主要内容 -->
      <div v-else class="account-content">
        <!-- 用户订阅状态 -->
        <UserSubscriptionStatus />

        <!-- 订单历史 -->
        <UserOrderHistory />

        <!-- 快捷操作 -->
        <div class="quick-actions">
          <h2 class="section-title">快捷操作</h2>
          <div class="actions-grid">
            <router-link to="/plans" class="action-card">
              <div class="action-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="action-content">
                <h3 class="action-title">升级套餐</h3>
                <p class="action-description">查看更多套餐选项</p>
              </div>
            </router-link>

            <router-link to="/profile" class="action-card">
              <div class="action-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="action-content">
                <h3 class="action-title">个人设置</h3>
                <p class="action-description">管理个人信息</p>
              </div>
            </router-link>

            <router-link to="/ai-roles" class="action-card">
              <div class="action-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="action-content">
                <h3 class="action-title">开始聊天</h3>
                <p class="action-description">选择AI角色开始对话</p>
              </div>
            </router-link>
          </div>
        </div>
      </div>

      <!-- 全局错误提示 -->
      <div v-if="userStatsStore.error" class="error-toast">
        {{ userStatsStore.error }}
        <button @click="userStatsStore.clearError()" class="error-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { useUserStatsStore } from '@/stores/userStats'
import { userAPI } from '@/services/api'
import UserSubscriptionStatus from '@/components/payment/UserSubscriptionStatus.vue'
import UserOrderHistory from '@/components/payment/UserOrderHistory.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()
const userStatsStore = useUserStatsStore()

// 计算属性
const hasAnyData = computed(() => {
  return authStore.isVip || userStatsStore.totalOrders > 0 || userStatsStore.totalMessages > 0
})

// 加载数据
async function loadData() {
  await Promise.all([
    // 刷新用户信息（包含会员状态）
    refreshUserProfile(),
    // 加载用户统计信息
    userStatsStore.loadStats()
  ])
}

// 刷新用户资料
async function refreshUserProfile() {
  try {
    const response = await userAPI.getProfile()
    authStore.setUser(response.data)
  } catch (error) {
    console.error('Failed to refresh user profile:', error)
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
    return
  }

  // 加载数据
  await loadData()
})
</script>

<style scoped>
.account-view {
  min-height: 100vh;
  background: var(--color-background-dark);
  padding: var(--space-xl) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  width: 100%;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 主要内容 */
.account-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3xl);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 快捷操作 */
.quick-actions {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-xl);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  transition: all 0.2s ease;
}

.action-card:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-purple);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass);
}

.action-icon {
  width: 48px;
  height: 48px;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 错误提示 */
.error-toast {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-glass);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  z-index: var(--z-index-modal);
  animation: slideInUp 0.3s ease;
}

.error-close {
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-view {
    padding: var(--space-lg) 0;
  }

  .container {
    padding: 0 var(--space-md);
  }

  .page-header {
    margin-bottom: var(--space-xl);
  }

  .page-title {
    font-size: 2rem;
  }

  .page-description {
    font-size: var(--font-size-base);
  }

  .account-content {
    gap: var(--space-xl);
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: var(--space-sm);
  }
}
</style>
