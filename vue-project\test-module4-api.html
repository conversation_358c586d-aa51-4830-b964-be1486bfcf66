<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块四 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .user-info {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #9B59B6;
        }
        .session-item {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #FFD60A;
        }
    </style>
</head>
<body>
    <h1>模块四 API 测试 - 用户中心</h1>
    
    <div class="test-section">
        <h2>1. 用户认证</h2>
        <input type="email" id="auth-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="auth-password" placeholder="密码" value="password123">
        <button onclick="testLogin()">登录获取Token</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 获取当前用户信息</h2>
        <button onclick="testGetCurrentUser()">获取用户信息</button>
        <div id="user-result" class="result"></div>
        <div id="user-display"></div>
    </div>

    <div class="test-section">
        <h2>3. 获取用户聊天会话</h2>
        <button onclick="testGetUserChats()">获取聊天历史</button>
        <div id="chats-result" class="result"></div>
        <div id="chats-display"></div>
    </div>

    <div class="test-section">
        <h2>4. 修改密码</h2>
        <input type="password" id="old-password" placeholder="当前密码" value="password123">
        <input type="password" id="new-password" placeholder="新密码" value="newpassword123">
        <button onclick="testChangePassword()">修改密码</button>
        <div id="password-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 边界条件测试</h2>
        <button onclick="testInvalidToken()">测试无效Token</button>
        <button onclick="testWeakPassword()">测试弱密码</button>
        <button onclick="testSamePassword()">测试相同密码</button>
        <div id="boundary-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        function displayUserInfo(user) {
            const container = document.getElementById('user-display');
            if (!user) {
                container.innerHTML = '<p>没有用户信息</p>';
                return;
            }

            container.innerHTML = `
                <div class="user-info">
                    <h3>${user.email || '游客用户'}</h3>
                    <p><strong>账户类型:</strong> ${user.is_guest ? '游客用户' : '注册用户'}</p>
                    <p><strong>账户状态:</strong> ${user.status}</p>
                    <p><strong>今日消息数:</strong> ${user.daily_message_count || 0}</p>
                    <p><strong>剩余点数:</strong> ${user.message_credits || 0}</p>
                    <p><strong>注册时间:</strong> ${new Date(user.created_at).toLocaleString()}</p>
                    ${user.profile_summary ? `<p><strong>用户画像:</strong> ${user.profile_summary}</p>` : ''}
                </div>
            `;
        }

        function displayChatSessions(sessions) {
            const container = document.getElementById('chats-display');
            if (!sessions || sessions.length === 0) {
                container.innerHTML = '<p>没有找到聊天记录</p>';
                return;
            }

            container.innerHTML = sessions.map(session => `
                <div class="session-item">
                    <strong>${session.title}</strong><br>
                    AI角色: ${session.ai_role_name}<br>
                    消息数: ${session.message_count || 0}<br>
                    最后更新: ${new Date(session.updated_at).toLocaleString()}
                </div>
            `).join('');
        }

        async function testLogin() {
            const email = document.getElementById('auth-email').value;
            const password = document.getElementById('auth-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = data.data.token;
                    displayResult('auth-result', data);
                } else {
                    displayResult('auth-result', data, true);
                }
            } catch (error) {
                displayResult('auth-result', { error: error.message }, true);
            }
        }

        async function testGetCurrentUser() {
            if (!currentToken) {
                displayResult('user-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('user-result', data);
                    displayUserInfo(data.data);
                } else {
                    displayResult('user-result', data, true);
                }
            } catch (error) {
                displayResult('user-result', { error: error.message }, true);
            }
        }

        async function testGetUserChats() {
            if (!currentToken) {
                displayResult('chats-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/chats`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('chats-result', data);
                    displayChatSessions(data.data);
                } else {
                    displayResult('chats-result', data, true);
                }
            } catch (error) {
                displayResult('chats-result', { error: error.message }, true);
            }
        }

        async function testChangePassword() {
            const oldPassword = document.getElementById('old-password').value;
            const newPassword = document.getElementById('new-password').value;
            
            if (!currentToken) {
                displayResult('password-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/password`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_password: oldPassword,
                        new_password: newPassword
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('password-result', data);
                } else {
                    displayResult('password-result', data, true);
                }
            } catch (error) {
                displayResult('password-result', { error: error.message }, true);
            }
        }

        async function testInvalidToken() {
            try {
                const response = await fetch(`${API_BASE_URL}/me`, {
                    headers: {
                        'Authorization': 'Bearer invalid_token'
                    }
                });
                
                const data = await response.json();
                displayResult('boundary-result', { test: '无效Token', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: '无效Token', error: error.message }, true);
            }
        }

        async function testWeakPassword() {
            if (!currentToken) {
                displayResult('boundary-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/password`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_password: 'password123',
                        new_password: '123'
                    })
                });
                
                const data = await response.json();
                displayResult('boundary-result', { test: '弱密码', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: '弱密码', error: error.message }, true);
            }
        }

        async function testSamePassword() {
            if (!currentToken) {
                displayResult('boundary-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/password`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_password: 'password123',
                        new_password: 'password123'
                    })
                });
                
                const data = await response.json();
                displayResult('boundary-result', { test: '相同密码', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: '相同密码', error: error.message }, true);
            }
        }
    </script>
</body>
</html>
