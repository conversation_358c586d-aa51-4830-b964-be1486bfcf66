<template>
  <Teleport to="body">
    <div v-if="isVisible" class="new-chat-overlay" @click="handleOverlayClick">
      <div class="new-chat-modal" @click.stop>
        <button class="modal-close" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title">选择AI伙伴</h2>
            <p class="modal-description">选择一个AI角色开始新的对话</p>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner">
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <p class="loading-text">正在加载AI角色...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-state">
            <div class="error-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h3 class="error-title">加载失败</h3>
            <p class="error-message">{{ error }}</p>
            <button class="btn btn-primary" @click="loadAIRoles">
              重试
            </button>
          </div>

          <!-- AI角色列表 -->
          <div v-else class="roles-list">
            <div
              v-for="role in aiRoles"
              :key="role.id"
              class="role-item"
              @click="selectRole(role)"
              :disabled="isCreating"
            >
              <div class="role-avatar">
                <img 
                  v-if="role.avatar_url" 
                  :src="role.avatar_url" 
                  :alt="role.name"
                  class="avatar-image"
                  @error="handleImageError"
                />
                <div v-else class="avatar-placeholder">
                  <span class="avatar-initial">{{ role.name.charAt(0) }}</span>
                </div>
              </div>
              
              <div class="role-info">
                <h3 class="role-name">{{ role.name }}</h3>
                <p class="role-description">{{ role.description }}</p>
              </div>
              
              <div class="role-action">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- 创建中状态 -->
          <div v-if="isCreating" class="creating-overlay">
            <div class="creating-content">
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <p>正在创建对话...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { aiRoleAPI } from '@/services/api'
import { useChatStore } from '@/stores/chat'

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['close', 'chat-created'])

// 状态管理
const chatStore = useChatStore()

// 响应式数据
const isVisible = ref(props.visible)
const aiRoles = ref([])
const isLoading = ref(false)
const isCreating = ref(false)
const error = ref('')

// 监听props变化
watch(() => props.visible, (newValue) => {
  isVisible.value = newValue
  if (newValue) {
    loadAIRoles()
    // 防止背景滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
})

// 加载AI角色列表
async function loadAIRoles() {
  isLoading.value = true
  error.value = ''

  try {
    const roles = await aiRoleAPI.getAIRoles()
    aiRoles.value = Array.isArray(roles) ? roles : []
  } catch (err) {
    error.value = err.message || '加载AI角色失败，请稍后重试'
    aiRoles.value = []
  } finally {
    isLoading.value = false
  }
}

// 选择角色并创建会话
async function selectRole(role) {
  if (isCreating.value) return

  isCreating.value = true
  error.value = ''

  try {
    const newSession = await chatStore.createSession(role.id)
    emit('chat-created', newSession)
  } catch (err) {
    error.value = err.message || '创建对话失败，请稍后重试'
  } finally {
    isCreating.value = false
  }
}

// 关闭模态框
function closeModal() {
  if (isCreating.value) return // 创建中不允许关闭
  
  isVisible.value = false
  document.body.style.overflow = ''
  emit('close')
}

// 处理点击遮罩层
function handleOverlayClick() {
  closeModal()
}

// 处理图片加载错误
function handleImageError(event) {
  if (event.target) {
    event.target.style.display = 'none'
    const placeholder = event.target.parentElement?.querySelector('.avatar-placeholder')
    if (placeholder) {
      placeholder.style.display = 'flex'
    }
  }
}

// 组件卸载时恢复滚动
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style scoped>
.new-chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--space-lg);
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.new-chat-modal {
  position: relative;
  background: var(--color-background-dark);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-glass);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  max-width: 600px;
  overflow: hidden;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  width: 32px;
  height: 32px;
  background: var(--color-surface-primary);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 1;
}

.modal-close:hover {
  background: var(--color-surface-secondary);
  color: var(--color-text-primary);
  transform: scale(1.05);
}

.modal-content {
  position: relative;
  padding: var(--space-xl);
  padding-top: calc(var(--space-xl) + 32px + var(--space-lg));
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  letter-spacing: -0.02em;
}

.modal-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* 加载和错误状态 */
.loading-state, .error-state {
  text-align: center;
  padding: var(--space-xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 角色列表 */
.roles-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.role-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
}

.role-item:hover:not([disabled]) {
  background: var(--color-surface-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
  border-color: var(--color-accent-purple);
}

.role-item[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.role-avatar {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-md);
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.role-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.role-action {
  flex-shrink: 0;
  color: var(--color-text-muted);
  transition: color 0.2s ease;
}

.role-item:hover .role-action {
  color: var(--color-accent-purple);
}

/* 创建中覆盖层 */
.creating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.creating-content {
  text-align: center;
  color: var(--color-text-primary);
}

.creating-content p {
  margin-top: var(--space-md);
  font-size: var(--font-size-base);
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .new-chat-overlay {
    padding: var(--space-md);
  }
  
  .modal-content {
    padding: var(--space-lg);
    padding-top: calc(var(--space-lg) + 32px + var(--space-md));
  }
  
  .modal-close {
    top: var(--space-md);
    right: var(--space-md);
  }

  .role-item {
    padding: var(--space-md);
  }

  .role-avatar {
    width: 40px;
    height: 40px;
  }
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--color-surface-primary);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--color-surface-secondary);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}
</style>
