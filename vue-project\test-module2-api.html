<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块二 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 100px;
        }
        .role-card {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #9B59B6;
        }
        .role-name {
            font-weight: bold;
            color: #FFD60A;
            margin-bottom: 5px;
        }
        .role-description {
            color: #E0E0E0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>模块二 API 测试 - AI角色展示与选择</h1>
    
    <div class="test-section">
        <h2>1. 获取AI角色列表</h2>
        <button onclick="testGetAIRoles()">测试获取角色列表</button>
        <div id="roles-result" class="result"></div>
        <div id="roles-display"></div>
    </div>

    <div class="test-section">
        <h2>2. 获取AI角色详情</h2>
        <input type="number" id="role-id" placeholder="角色ID" value="1" min="1">
        <button onclick="testGetAIRoleDetail()">测试获取角色详情</button>
        <div id="detail-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 边界条件测试</h2>
        <button onclick="testInvalidRoleId()">测试无效ID (0)</button>
        <button onclick="testNegativeRoleId()">测试负数ID (-1)</button>
        <button onclick="testNonExistentRoleId()">测试不存在ID (999)</button>
        <button onclick="testStringRoleId()">测试字符串ID (abc)</button>
        <div id="boundary-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        function displayRoles(roles) {
            const container = document.getElementById('roles-display');
            if (!roles || roles.length === 0) {
                container.innerHTML = '<p>没有找到AI角色</p>';
                return;
            }

            container.innerHTML = roles.map(role => `
                <div class="role-card">
                    <div class="role-name">${role.name}</div>
                    <div class="role-description">${role.description}</div>
                    <div style="margin-top: 10px; font-size: 12px; color: #888;">
                        ID: ${role.id} | 头像: ${role.avatar_url || '无'}
                    </div>
                </div>
            `).join('');
        }

        async function testGetAIRoles() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('roles-result', data);
                    displayRoles(data.data);
                } else {
                    displayResult('roles-result', data, true);
                }
            } catch (error) {
                displayResult('roles-result', { error: error.message }, true);
            }
        }

        async function testGetAIRoleDetail() {
            const roleId = document.getElementById('role-id').value;
            
            if (!roleId) {
                displayResult('detail-result', { error: '请输入角色ID' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles/${roleId}`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('detail-result', data);
                } else {
                    displayResult('detail-result', data, true);
                }
            } catch (error) {
                displayResult('detail-result', { error: error.message }, true);
            }
        }

        async function testInvalidRoleId() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles/0`);
                const data = await response.json();
                displayResult('boundary-result', { test: 'ID=0', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: 'ID=0', error: error.message }, true);
            }
        }

        async function testNegativeRoleId() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles/-1`);
                const data = await response.json();
                displayResult('boundary-result', { test: 'ID=-1', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: 'ID=-1', error: error.message }, true);
            }
        }

        async function testNonExistentRoleId() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles/999`);
                const data = await response.json();
                displayResult('boundary-result', { test: 'ID=999', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: 'ID=999', error: error.message }, true);
            }
        }

        async function testStringRoleId() {
            try {
                const response = await fetch(`${API_BASE_URL}/ai-roles/abc`);
                const data = await response.json();
                displayResult('boundary-result', { test: 'ID=abc', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: 'ID=abc', error: error.message }, true);
            }
        }

        // 页面加载时自动测试角色列表
        window.onload = function() {
            testGetAIRoles();
        };
    </script>
</body>
</html>
