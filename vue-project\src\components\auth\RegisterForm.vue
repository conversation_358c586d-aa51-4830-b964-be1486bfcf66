<template>
  <div class="register-form">
    <div class="form-header">
      <h2 class="text-xl font-semibold text-primary">创建账户</h2>
      <p class="text-sm text-muted">注册账户以保存您的聊天记录</p>
    </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <div class="form-group">
        <label for="email" class="form-label">邮箱地址</label>
        <input
          id="email"
          v-model="formData.email"
          type="email"
          class="input"
          :class="{ 'input-error': errors.email }"
          placeholder="请输入您的邮箱地址"
          required
          :disabled="isLoading"
          @input="handleEmailInput"
        />
        <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
      </div>

      <div class="form-group">
        <label for="password" class="form-label">密码</label>
        <div class="password-input-wrapper">
          <input
            id="password"
            v-model="formData.password"
            :type="showPassword ? 'text' : 'password'"
            class="input"
            :class="{ 'input-error': errors.password }"
            placeholder="请设置您的密码（至少6位）"
            required
            :disabled="isLoading"
            @input="handlePasswordInput"
          />
          <button
            type="button"
            class="password-toggle"
            @click="showPassword = !showPassword"
            :disabled="isLoading"
          >
            {{ showPassword ? '隐藏' : '显示' }}
          </button>
        </div>
        <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
      </div>

      <div class="form-group">
        <label for="confirmPassword" class="form-label">确认密码</label>
        <div class="password-input-wrapper">
          <input
            id="confirmPassword"
            v-model="formData.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            class="input"
            :class="{ 'input-error': errors.confirmPassword }"
            placeholder="请再次输入密码"
            required
            :disabled="isLoading"
            @input="handleConfirmPasswordInput"
          />
          <button
            type="button"
            class="password-toggle"
            @click="showConfirmPassword = !showConfirmPassword"
            :disabled="isLoading"
          >
            {{ showConfirmPassword ? '隐藏' : '显示' }}
          </button>
        </div>
        <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
      </div>

      <div class="form-actions">
        <button
          type="submit"
          class="btn btn-primary"
          :disabled="isLoading || !isFormValid"
        >
          <span v-if="isLoading" class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </span>
          <span v-else>注册</span>
        </button>

        <div class="form-links">
          <button
            type="button"
            class="link-button"
            @click="$emit('switch-to-login')"
            :disabled="isLoading"
          >
            已有账户？立即登录
          </button>
        </div>
      </div>

      <div v-if="error" class="error-alert">
        {{ error }}
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useAuthStore } from '@/stores/counter'
import { authAPI } from '@/services/api'

// 定义事件
const emit = defineEmits(['switch-to-login', 'register-success'])

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const formData = reactive({
  email: '',
  password: '',
  confirmPassword: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)
const error = ref('')
const errors = reactive({
  email: '',
  password: '',
  confirmPassword: ''
})

// 计算属性
const isFormValid = computed(() => {
  return formData.email && 
         formData.password && 
         formData.confirmPassword &&
         !errors.email && 
         !errors.password && 
         !errors.confirmPassword
})

// 表单验证
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email) {
    return '请输入邮箱地址'
  }
  if (!emailRegex.test(email)) {
    return '请输入有效的邮箱地址'
  }
  return ''
}

function validatePassword(password) {
  if (!password) {
    return '请输入密码'
  }
  if (password.length < 6) {
    return '密码长度至少6位'
  }
  if (password.length > 50) {
    return '密码长度不能超过50位'
  }
  return ''
}

function validateConfirmPassword(password, confirmPassword) {
  if (!confirmPassword) {
    return '请确认密码'
  }
  if (password !== confirmPassword) {
    return '两次输入的密码不一致'
  }
  return ''
}

function validateForm() {
  errors.email = validateEmail(formData.email)
  errors.password = validatePassword(formData.password)
  errors.confirmPassword = validateConfirmPassword(formData.password, formData.confirmPassword)
  
  return !errors.email && !errors.password && !errors.confirmPassword
}

// 处理表单提交
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    const response = await authAPI.register(formData.email, formData.password)

    // 保存用户信息和token
    authStore.setUser(response.user)
    authStore.setToken(response.token)
    authStore.saveUserToStorage()

    emit('register-success')
  } catch (err) {
    error.value = err.message || '注册失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 清除错误信息
function clearError() {
  error.value = ''
}

// 监听输入变化，清除对应的错误信息
function handleEmailInput() {
  if (errors.email) {
    errors.email = ''
  }
  clearError()
}

function handlePasswordInput() {
  if (errors.password) {
    errors.password = ''
  }
  if (errors.confirmPassword && formData.confirmPassword) {
    errors.confirmPassword = validateConfirmPassword(formData.password, formData.confirmPassword)
  }
  clearError()
}

function handleConfirmPasswordInput() {
  if (errors.confirmPassword) {
    errors.confirmPassword = ''
  }
  clearError()
}
</script>

<style scoped>
.register-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.form-header h2 {
  margin-bottom: var(--space-sm);
}

.form-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-muted);
  font-size: var(--font-size-xs);
  cursor: pointer;
  padding: var(--space-xs);
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--color-text-secondary);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.input-error {
  border-color: var(--color-error);
}

.error-message {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: var(--space-xs);
}

.form-actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.form-links {
  text-align: center;
}

.link-button {
  background: none;
  border: none;
  color: var(--color-accent-purple);
  font-size: var(--font-size-sm);
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.link-button:hover {
  color: var(--color-accent-purple-light);
}

.link-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.error-alert {
  padding: var(--space-md);
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--color-error);
  border-radius: var(--border-radius-sm);
  color: var(--color-error);
  font-size: var(--font-size-sm);
  text-align: center;
}
</style>
