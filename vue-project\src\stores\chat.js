import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { chatAPI } from '@/services/api'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const sessions = ref([])
  const currentSession = ref(null)
  const messages = ref([])
  const isLoading = ref(false)
  const isSending = ref(false)
  const error = ref('')

  // 计算属性
  const currentSessionUuid = computed(() => currentSession.value?.uuid)
  const hasMessages = computed(() => messages.value.length > 0)
  const canSendMessage = computed(() => !isSending.value && currentSession.value)

  // 方法
  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = ''
  }

  function setLoading(loading) {
    isLoading.value = loading
  }

  function setSending(sending) {
    isSending.value = sending
  }

  // 设置当前会话
  function setCurrentSession(session) {
    currentSession.value = session
    messages.value = []
    clearError()
  }

  // 添加消息到当前会话
  function addMessage(message) {
    messages.value.push({
      ...message,
      id: Date.now() + Math.random(), // 临时ID
      created_at: message.created_at || new Date().toISOString()
    })
  }

  // 更新消息内容（用于流式传输）
  function updateMessage(messageId, updates) {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex] = { ...messages.value[messageIndex], ...updates }
    }
  }

  // 加载会话列表
  async function loadSessions() {
    setLoading(true)
    clearError()

    try {
      const sessionList = await chatAPI.getChatSessions()
      sessions.value = Array.isArray(sessionList) ? sessionList : []
    } catch (err) {
      setError(err.message || '加载会话列表失败')
      sessions.value = []
    } finally {
      setLoading(false)
    }
  }

  // 创建新会话
  async function createSession(aiRoleId) {
    setLoading(true)
    clearError()

    try {
      const newSession = await chatAPI.createChatSession(aiRoleId)

      console.log('Created session:', newSession) // 调试日志

      // 添加到会话列表的开头
      sessions.value.unshift(newSession)

      // 设置为当前会话
      setCurrentSession(newSession)

      return newSession
    } catch (err) {
      console.error('Create session error:', err) // 调试日志
      setError(err.message || '创建会话失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 加载会话消息
  async function loadMessages(sessionUuid) {
    if (!sessionUuid) return

    setLoading(true)
    clearError()

    try {
      const messageList = await chatAPI.getChatMessages(sessionUuid)
      messages.value = Array.isArray(messageList) ? messageList.map(msg => ({
        ...msg,
        id: msg.id || Date.now() + Math.random()
      })) : []
    } catch (err) {
      setError(err.message || '加载消息失败')
      messages.value = []
    } finally {
      setLoading(false)
    }
  }

  // 发送消息
  async function sendMessage(content) {
    if (!currentSession.value || !content.trim()) {
      return
    }

    setSending(true)
    clearError()

    // 添加用户消息到界面
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: content.trim(),
      created_at: new Date().toISOString()
    }
    addMessage(userMessage)

    // 添加AI消息占位符
    const aiMessageId = Date.now() + 1
    const aiMessage = {
      id: aiMessageId,
      role: 'assistant',
      content: '',
      created_at: new Date().toISOString(),
      streaming: true
    }
    addMessage(aiMessage)

    try {
      const response = await chatAPI.sendMessage(currentSession.value.uuid, content)

      // 更新AI消息
      updateMessage(aiMessageId, {
        content: response.content || '收到回复',
        created_at: response.created_at || new Date().toISOString(),
        streaming: false
      })

      // 更新会话的最后更新时间
      if (currentSession.value) {
        const updateTime = response.created_at || new Date().toISOString()
        currentSession.value.updated_at = updateTime

        // 更新会话列表中的对应项
        const sessionIndex = sessions.value.findIndex(s => s.uuid === currentSession.value.uuid)
        if (sessionIndex !== -1) {
          sessions.value[sessionIndex].updated_at = updateTime
          // 将会话移到列表顶部
          const updatedSession = sessions.value.splice(sessionIndex, 1)[0]
          sessions.value.unshift(updatedSession)
        }
      }

    } catch (err) {
      console.error('Send message error:', err) // 调试日志
      // 标记AI消息为错误状态
      updateMessage(aiMessageId, {
        content: '抱歉，我暂时无法回复。请稍后再试。',
        streaming: false,
        error: true
      })
      setError(err.message || '发送消息失败')
    } finally {
      setSending(false)
    }
  }

  // 切换到指定会话
  async function switchToSession(sessionUuid) {
    const session = sessions.value.find(s => s.uuid === sessionUuid)
    if (session) {
      setCurrentSession(session)
      await loadMessages(sessionUuid)
    } else {
      // 如果本地没有找到会话，尝试从API获取
      try {
        const sessionDetail = await chatAPI.getChatSessionDetail(sessionUuid)
        console.log('Fetched session detail:', sessionDetail) // 调试日志
        setCurrentSession(sessionDetail)
        await loadMessages(sessionUuid)
      } catch (err) {
        console.error('Failed to fetch session detail:', err)
        setError('会话不存在或已被删除')
      }
    }
  }

  // 删除会话
  async function deleteSession(sessionUuid) {
    try {
      await chatAPI.deleteChatSession(sessionUuid)
      
      // 从列表中移除
      sessions.value = sessions.value.filter(s => s.uuid !== sessionUuid)
      
      // 如果删除的是当前会话，清空当前会话
      if (currentSession.value?.uuid === sessionUuid) {
        currentSession.value = null
        messages.value = []
      }
      
    } catch (err) {
      setError(err.message || '删除会话失败')
      throw err
    }
  }

  // 清空所有状态
  function clearAll() {
    sessions.value = []
    currentSession.value = null
    messages.value = []
    clearError()
  }

  return {
    // 状态
    sessions,
    currentSession,
    messages,
    isLoading,
    isSending,
    error,
    
    // 计算属性
    currentSessionUuid,
    hasMessages,
    canSendMessage,
    
    // 方法
    setError,
    clearError,
    setLoading,
    setSending,
    setCurrentSession,
    addMessage,
    updateMessage,
    loadSessions,
    createSession,
    loadMessages,
    sendMessage,
    switchToSession,
    deleteSession,
    clearAll
  }
})
