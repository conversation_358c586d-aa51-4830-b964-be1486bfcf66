import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { chatAPI } from '@/services/api'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const sessions = ref([])
  const currentSessionUuid = ref(null)
  const messages = ref([])
  const isLoading = ref(false)
  const isSending = ref(false)
  const error = ref('')
  const loadingSessionUuid = ref(null) // 当前正在加载的会话UUID

  // 计算属性
  const currentSession = computed(() => {
    return sessions.value.find(session => session.uuid === currentSessionUuid.value)
  })

  const hasMessages = computed(() => messages.value.length > 0)

  // 方法
  function setCurrentSession(sessionUuid) {
    currentSessionUuid.value = sessionUuid
  }

  function addMessage(message) {
    // 为消息添加唯一ID
    if (!message.id) {
      message.id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
    messages.value.push(message)
  }

  function updateLastMessage(content) {
    if (messages.value.length > 0) {
      const lastMessage = messages.value[messages.value.length - 1]
      lastMessage.content = content
    }
  }

  function setMessages(newMessages) {
    // 消息去重：基于角色、内容、创建时间进行去重
    const uniqueMessages = []
    const messageKeys = new Set()

    for (const message of newMessages) {
      // 创建消息的唯一标识
      const messageKey = `${message.role}-${message.content}-${message.created_at}`

      if (!messageKeys.has(messageKey)) {
        messageKeys.add(messageKey)
        uniqueMessages.push({
          ...message,
          id: message.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        })
      }
    }

    messages.value = uniqueMessages
  }

  function setSessions(newSessions) {
    sessions.value = newSessions
  }

  function addSession(session) {
    sessions.value.unshift(session)
  }

  function setLoading(loading) {
    isLoading.value = loading
  }

  function setSending(sending) {
    isSending.value = sending
  }

  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = ''
  }

  // 加载会话列表
  async function loadSessions() {
    setLoading(true)
    clearError()

    try {
      const sessionList = await chatAPI.getChatSessions()
      setSessions(Array.isArray(sessionList) ? sessionList : [])
    } catch (err) {
      setError(err.message || '加载会话列表失败')
      setSessions([])
    } finally {
      setLoading(false)
    }
  }

  // 创建新会话
  async function createSession(aiRoleId) {
    clearError()

    try {
      const newSession = await chatAPI.createChatSession(aiRoleId)
      addSession(newSession)
      setCurrentSession(newSession.uuid)
      setMessages([]) // 清空消息列表
      return newSession
    } catch (err) {
      setError(err.message || '创建会话失败')
      throw err
    }
  }

  // 加载会话消息
  async function loadMessages(sessionUuid) {
    if (!sessionUuid) return

    // 防止竞态条件：如果正在加载其他会话，忽略
    if (loadingSessionUuid.value && loadingSessionUuid.value !== sessionUuid) {
      return
    }

    loadingSessionUuid.value = sessionUuid
    setLoading(true)
    clearError()

    try {
      const messageList = await chatAPI.getChatMessages(sessionUuid)

      // 再次检查是否还是当前要加载的会话（防止用户快速切换）
      if (loadingSessionUuid.value === sessionUuid) {
        setMessages(Array.isArray(messageList) ? messageList : [])
      }
    } catch (err) {
      // 只有在还是当前加载的会话时才设置错误
      if (loadingSessionUuid.value === sessionUuid) {
        setError(err.message || '加载消息失败')
        setMessages([])
      }
    } finally {
      if (loadingSessionUuid.value === sessionUuid) {
        setLoading(false)
        loadingSessionUuid.value = null
      }
    }
  }

  // 发送消息（流式）
  async function sendMessage(content) {
    if (!currentSessionUuid.value || !content.trim()) return

    setSending(true)
    clearError()

    // 添加用户消息
    const userMessage = {
      role: 'user',
      content: content.trim(),
      created_at: new Date().toISOString(),
      streaming: false
    }
    addMessage(userMessage)

    // 添加AI消息占位符
    const aiMessage = {
      role: 'assistant',
      content: '',
      created_at: new Date().toISOString(),
      streaming: true
    }
    addMessage(aiMessage)

    try {
      await chatAPI.sendMessageStream(
        currentSessionUuid.value,
        content.trim(),
        // onChunk
        (event) => {
          if (event.fullContent !== undefined) {
            updateLastMessage(event.fullContent)
          } else if (event.content) {
            const lastMessage = messages.value[messages.value.length - 1]
            lastMessage.content += event.content
          }
        },
        // onComplete
        (finalMessage) => {
          const lastMessage = messages.value[messages.value.length - 1]
          lastMessage.content = finalMessage.content
          lastMessage.streaming = false
          lastMessage.created_at = finalMessage.created_at
        },
        // onError
        (error) => {
          const lastMessage = messages.value[messages.value.length - 1]
          lastMessage.streaming = false
          lastMessage.error = true
          lastMessage.content = '消息发送失败，请重试'
          setError(error.message || '发送消息失败')
        }
      )
    } catch (err) {
      const lastMessage = messages.value[messages.value.length - 1]
      lastMessage.streaming = false
      lastMessage.error = true
      lastMessage.content = '消息发送失败，请重试'
      setError(err.message || '发送消息失败')
    } finally {
      setSending(false)
    }
  }

  // 切换会话
  async function switchSession(sessionUuid) {
    if (sessionUuid === currentSessionUuid.value) return

    setCurrentSession(sessionUuid)
    await loadMessages(sessionUuid)
  }

  // 清空当前会话
  function clearCurrentSession() {
    setCurrentSession(null)
    setMessages([])
  }

  return {
    // 状态
    sessions,
    currentSessionUuid,
    messages,
    isLoading,
    isSending,
    error,

    // 计算属性
    currentSession,
    hasMessages,

    // 方法
    setCurrentSession,
    addMessage,
    updateLastMessage,
    setMessages,
    setSessions,
    addSession,
    setLoading,
    setSending,
    setError,
    clearError,
    loadSessions,
    createSession,
    loadMessages,
    sendMessage,
    switchSession,
    clearCurrentSession
  }
})
