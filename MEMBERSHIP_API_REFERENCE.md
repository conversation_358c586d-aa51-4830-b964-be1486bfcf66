# 用户会员状态 API 接口文档

## 📋 接口概览

| 接口 | 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|------|
| 获取用户信息 | GET | `/api/v1/user/profile` | 获取用户详细信息（含会员状态） | 需要 |
| 获取用户统计 | GET | `/api/v1/user/stats` | 获取用户使用统计（含会员状态） | 需要 |

## 🔧 接口详情

### 1. 获取用户信息

**请求**
```http
GET /api/v1/user/profile
Authorization: Bearer {token}
```

**响应**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "uuid": "4b50a5cb-883b-45ae-8399-17e46d9b5939",
    "email": "<EMAIL>",
    "is_guest": false,
    "status": "active",
    "daily_message_count": 3,
    "message_credits": 100,
    "profile_summary": null,
    "created_at": "2025-07-22T11:06:12.000Z",
    
    // 🆕 会员状态字段
    "is_vip": true,                    // 是否为VIP会员
    "subscription_info": {             // 订阅信息（非会员时为null）
      "plan_name": "月度会员",         // 套餐名称
      "start_date": "2025-07-22T11:08:10.000Z",  // 开始时间
      "end_date": "2025-08-21T11:08:10.000Z",    // 结束时间
      "status": "active"               // 订阅状态: active/expired/cancelled
    }
  }
}
```

**字段说明**
- `is_vip`: 布尔值，表示用户是否为VIP会员
- `subscription_info`: 对象或null，包含订阅详情
  - 当用户是VIP时：包含完整订阅信息
  - 当用户非VIP时：为null

### 2. 获取用户统计

**请求**
```http
GET /api/v1/user/stats
Authorization: Bearer {token}
```

**响应**
```json
{
  "code": 200,
  "message": "User statistics retrieved successfully",
  "data": {
    "total_sessions": 5,        // 总聊天会话数
    "total_messages": 25,       // 总发送消息数
    "total_orders": 2,          // 总订单数
    "total_spent": 220.00,      // 总消费金额（数字类型）
    "daily_message_count": 3,   // 今日消息数
    "message_credits": 100,     // 剩余消息点数
    "is_vip": true,            // 🆕 VIP状态
    "member_since": "2025-07-22T11:06:12.000Z"  // 注册时间
  }
}
```

## 🔄 状态变化时机

### 用户成为VIP的情况
1. **购买订阅套餐**: 用户购买月度/年度会员套餐并支付成功
2. **订阅续费**: 现有订阅到期前续费
3. **管理员手动设置**: 后台管理员手动添加订阅

### 用户失去VIP的情况
1. **订阅过期**: 订阅到期且未续费
2. **订阅取消**: 用户或管理员取消订阅
3. **支付失败**: 自动续费失败

## 💡 前端使用建议

### 1. 实时检查VIP状态
```javascript
// 在需要VIP功能的地方检查
async checkVipStatus() {
  const response = await this.$http.get('/api/v1/user/profile')
  const isVip = response.data.data.is_vip
  
  if (!isVip) {
    this.$message.warning('此功能需要VIP会员，请先升级')
    this.$router.push('/plans')
    return false
  }
  return true
}
```

### 2. 显示订阅剩余时间
```javascript
// 计算订阅剩余天数
getRemainingDays(endDate) {
  const end = new Date(endDate)
  const now = new Date()
  const diffTime = end - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

// 使用示例
const profile = await this.getUserProfile()
if (profile.is_vip && profile.subscription_info) {
  const remainingDays = this.getRemainingDays(profile.subscription_info.end_date)
  console.log(`VIP还剩 ${remainingDays} 天`)
}
```

### 3. 支付成功后刷新状态
```javascript
// 支付成功回调
async onPaymentSuccess() {
  // 等待后端处理完成
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 重新获取用户信息
  const profile = await this.getUserProfile()
  
  if (profile.is_vip) {
    this.$message.success('恭喜成为VIP会员！')
    // 更新全局状态
    this.$store.commit('user/setProfile', profile)
  }
}
```

## 🎨 UI展示建议

### VIP标识显示
```html
<!-- 用户头像旁的VIP标识 -->
<div class="user-avatar">
  <img :src="userAvatar" alt="头像">
  <span v-if="userProfile.is_vip" class="vip-badge">VIP</span>
</div>

<!-- 个人中心的会员卡片 -->
<div v-if="userProfile.is_vip" class="membership-card">
  <h3>{{ userProfile.subscription_info.plan_name }}</h3>
  <p>有效期至: {{ formatDate(userProfile.subscription_info.end_date) }}</p>
  <p>剩余: {{ getRemainingDays(userProfile.subscription_info.end_date) }} 天</p>
</div>
```

### 非VIP用户引导
```html
<!-- 非VIP用户的升级提示 -->
<div v-if="!userProfile.is_vip" class="upgrade-prompt">
  <h3>升级VIP解锁更多功能</h3>
  <ul>
    <li>无限聊天次数</li>
    <li>专属AI角色</li>
    <li>优先客服支持</li>
  </ul>
  <button @click="goToPlans">立即升级</button>
</div>
```

## 🔍 错误处理

### 常见错误码
- `401`: 未登录或token过期
- `403`: 权限不足
- `404`: 用户不存在
- `500`: 服务器内部错误

### 错误处理示例
```javascript
async getUserProfile() {
  try {
    const response = await this.$http.get('/api/v1/user/profile')
    return response.data.data
  } catch (error) {
    if (error.response?.status === 401) {
      // 重新登录
      this.$router.push('/login')
    } else {
      this.$message.error('获取用户信息失败')
    }
    throw error
  }
}
```

## 📊 数据类型说明

### 订阅状态枚举
- `active`: 有效订阅
- `expired`: 已过期
- `cancelled`: 已取消

### 日期格式
- 所有日期字段均为ISO 8601格式: `YYYY-MM-DDTHH:mm:ss.sssZ`
- 前端显示时需要格式化为本地时间

### 金额格式
- `total_spent`: 数字类型，单位为元
- 前端显示时建议保留2位小数

## ✅ 测试验证

所有接口已通过完整测试：
- ✅ 普通用户返回 `is_vip: false`
- ✅ VIP用户返回 `is_vip: true` 和完整订阅信息
- ✅ 支付成功后状态实时更新
- ✅ 订阅过期后状态自动变更

## 🔗 相关文档

- [前端集成完整指南](./MEMBERSHIP_STATUS_FRONTEND_GUIDE.md)
- [API最佳实践](./API_BEST_PRACTICES_DOCUMENTATION.md)
- [项目完整文档](./README.md)

---

**简洁、准确、易用的API接口** 🚀
