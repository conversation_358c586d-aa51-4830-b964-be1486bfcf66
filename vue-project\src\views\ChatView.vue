<template>
  <div class="chat-view">
    <!-- 侧边栏 -->
    <div class="chat-sidebar" :class="{ 'sidebar-open': sidebarOpen }">
      <div class="sidebar-header">
        <h2 class="sidebar-title">聊天记录</h2>
        <button class="btn-new-chat" @click="startNewChat">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          新对话
        </button>
      </div>

      <div class="session-list">
        <!-- 加载状态 -->
        <div v-if="chatStore.isLoading && chatStore.sessions.length === 0" class="loading-sessions">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <p class="loading-text">加载中...</p>
        </div>

        <!-- 会话列表 -->
        <div v-else-if="chatStore.sessions.length > 0" class="sessions">
          <div
            v-for="session in chatStore.sessions"
            :key="session.uuid"
            class="session-item"
            :class="{ 'session-active': session.uuid === chatStore.currentSessionUuid }"
            @click="switchSession(session.uuid)"
          >
            <div class="session-info">
              <h3 class="session-title">{{ session.ai_role?.name || 'AI助手' }}</h3>
              <p class="session-preview">{{ getSessionPreview(session) }}</p>
              <span class="session-time">{{ formatTime(session.updated_at) }}</span>
            </div>
            <button
              class="btn-delete-session"
              @click.stop="deleteSession(session.uuid)"
              :disabled="chatStore.isLoading"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-sessions">
          <div class="empty-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <p class="empty-text">还没有聊天记录</p>
          <button class="btn btn-primary btn-sm" @click="startNewChat">
            开始新对话
          </button>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 移动端顶部栏 -->
      <div class="mobile-header">
        <button class="btn-sidebar-toggle" @click="toggleSidebar">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <h1 class="mobile-title">
          {{ chatStore.currentSession?.ai_role?.name || 'AI助手' }}
        </h1>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content">
        <!-- 欢迎界面 -->
        <div v-if="!chatStore.currentSession" class="welcome-screen">
          <div class="welcome-content">
            <div class="welcome-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                <path d="M12 16L13.09 22.26L22 23L13.09 23.74L12 30L10.91 23.74L2 23L10.91 22.26L12 16Z" fill="currentColor" opacity="0.6"/>
              </svg>
            </div>
            <h2 class="welcome-title">欢迎来到AI伙伴聊天</h2>
            <p class="welcome-description">
              选择一个AI角色开始对话，或者从左侧选择已有的聊天记录继续交流。
            </p>
            <div class="welcome-actions">
              <router-link to="/ai-roles" class="btn btn-primary">
                选择AI角色
              </router-link>
              <button class="btn btn-secondary" @click="startNewChat">
                开始新对话
              </button>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-else class="messages-container" ref="messagesContainer">
          <div class="messages-list">
            <!-- 会话开始提示 -->
            <div class="session-start">
              <div class="session-start-content">
                <div class="ai-avatar">
                  <img
                    v-if="chatStore.currentSession.ai_role?.avatar_url"
                    :src="chatStore.currentSession.ai_role.avatar_url"
                    :alt="chatStore.currentSession.ai_role.name"
                    class="avatar-image"
                  />
                  <div v-else class="avatar-placeholder">
                    <span class="avatar-initial">
                      {{ chatStore.currentSession.ai_role?.name?.charAt(0) || 'AI' }}
                    </span>
                  </div>
                </div>
                <h3 class="session-start-title">
                  与{{ chatStore.currentSession.ai_role?.name || 'AI助手' }}的对话
                </h3>
                <p class="session-start-description">
                  {{ chatStore.currentSession.ai_role?.description || '开始你们的对话吧！' }}
                </p>
              </div>
            </div>

            <!-- 消息列表 -->
            <div v-for="message in chatStore.messages" :key="message.id" class="message-wrapper">
              <ChatMessage
                :message="message"
                :ai-role="chatStore.currentSession.ai_role"
              />
            </div>

            <!-- 加载更多消息 -->
            <div v-if="chatStore.isLoading && chatStore.hasMessages" class="loading-messages">
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div v-if="chatStore.currentSession" class="chat-input-area">
        <ChatInput
          @send-message="handleSendMessage"
          :disabled="!chatStore.canSendMessage"
          :loading="chatStore.isSending"
        />
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="chatStore.error" class="error-toast">
      <div class="error-content">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
          <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
        </svg>
        <span>{{ chatStore.error }}</span>
        <button @click="chatStore.clearError" class="btn-close-error">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { useChatStore } from '@/stores/chat'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import ChatInput from '@/components/chat/ChatInput.vue'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()

// 响应式数据
const sidebarOpen = ref(false)
const messagesContainer = ref(null)

// 切换侧边栏
function toggleSidebar() {
  sidebarOpen.value = !sidebarOpen.value
}

// 开始新对话
function startNewChat() {
  router.push('/ai-roles')
}

// 切换会话
async function switchSession(sessionUuid) {
  try {
    await chatStore.switchToSession(sessionUuid)
    sidebarOpen.value = false // 移动端关闭侧边栏
    scrollToBottom()
  } catch (err) {
    console.error('切换会话失败:', err)
  }
}

// 删除会话
async function deleteSession(sessionUuid) {
  if (confirm('确定要删除这个对话吗？此操作无法撤销。')) {
    try {
      await chatStore.deleteSession(sessionUuid)
    } catch (err) {
      console.error('删除会话失败:', err)
    }
  }
}

// 处理发送消息
async function handleSendMessage(content) {
  await chatStore.sendMessage(content)
  scrollToBottom()
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 获取会话预览文本
function getSessionPreview(session) {
  if (session.last_message) {
    return session.last_message.content.substring(0, 50) + (session.last_message.content.length > 50 ? '...' : '')
  }
  return '开始新的对话...'
}

// 格式化时间
function formatTime(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now - date
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`

  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 处理路由参数
async function handleRouteParams() {
  const sessionId = route.params.sessionId
  const roleId = route.query.roleId

  if (sessionId) {
    // 如果有会话ID，切换到该会话
    await switchSession(sessionId)
  } else if (roleId) {
    // 如果有角色ID，创建新会话
    try {
      await chatStore.createSession(parseInt(roleId))
      // 更新URL，移除roleId参数
      router.replace({ name: 'chat', params: { sessionId: chatStore.currentSessionUuid } })
    } catch (err) {
      console.error('创建会话失败:', err)
      router.push('/ai-roles')
    }
  }
}

// 监听消息变化，自动滚动到底部
watch(() => chatStore.messages.length, () => {
  scrollToBottom()
})

// 监听路由变化
watch(() => route.params.sessionId, (newSessionId) => {
  if (newSessionId && newSessionId !== chatStore.currentSessionUuid) {
    switchSession(newSessionId)
  }
})

// 组件挂载
onMounted(async () => {
  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: {
        showAuth: 'guest',
        redirect: route.fullPath
      }
    })
    return
  }

  // 加载会话列表
  await chatStore.loadSessions()

  // 处理路由参数
  await handleRouteParams()
})

// 组件卸载时清理
onUnmounted(() => {
  // 可以在这里添加清理逻辑
})
</script>

<style scoped>
.chat-view {
  height: 100vh;
  display: flex;
  background: var(--color-background-dark);
  overflow: hidden;
}

/* 侧边栏 */
.chat-sidebar {
  width: 320px;
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border-right: 1px solid var(--color-border-glass);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-md);
}

.sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.btn-new-chat {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  border: none;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  white-space: nowrap;
}

.btn-new-chat:hover {
  background: var(--color-accent-purple-light);
  transform: translateY(-1px);
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-sm);
}

.loading-sessions {
  text-align: center;
  padding: var(--space-xl);
}

.loading-text {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  margin-top: var(--space-sm);
}

.sessions {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.session-item {
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  border: 1px solid transparent;
}

.session-item:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-border-glass);
}

.session-item.session-active {
  background: var(--color-accent-purple);
  border-color: var(--color-accent-purple-light);
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-active .session-title {
  color: var(--color-text-primary);
}

.session-preview {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin-bottom: var(--space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-active .session-preview {
  color: rgba(255, 255, 255, 0.8);
}

.session-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.session-active .session-time {
  color: rgba(255, 255, 255, 0.7);
}

.btn-delete-session {
  background: none;
  border: none;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
  opacity: 0;
  flex-shrink: 0;
}

.session-item:hover .btn-delete-session {
  opacity: 1;
}

.btn-delete-session:hover {
  background: var(--color-error);
  color: var(--color-text-primary);
}

.empty-sessions {
  text-align: center;
  padding: var(--space-3xl) var(--space-lg);
}

.empty-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-text {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 主聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.mobile-header {
  display: none;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md) var(--space-lg);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border-bottom: 1px solid var(--color-border-glass);
}

.btn-sidebar-toggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.btn-sidebar-toggle:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
}

.mobile-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.chat-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 欢迎界面 */
.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  color: var(--color-accent-purple);
  margin-bottom: var(--space-xl);
  display: flex;
  justify-content: center;
}

.welcome-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.welcome-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-xl);
}

.welcome-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.messages-list {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.session-start {
  text-align: center;
  padding: var(--space-xl) 0;
}

.session-start-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.ai-avatar {
  width: 80px;
  height: 80px;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-lg);
  object-fit: cover;
  border: 2px solid var(--color-border-glass);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-border-glass);
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.session-start-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.session-start-description {
  color: var(--color-text-secondary);
  max-width: 400px;
}

.loading-messages {
  text-align: center;
  padding: var(--space-lg);
}

/* 输入区域 */
.chat-input-area {
  padding: var(--space-lg);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border-top: 1px solid var(--color-border-glass);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: var(--space-lg);
  right: var(--space-lg);
  z-index: var(--z-index-modal);
  animation: slideInRight 0.3s ease;
}

.error-content {
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  box-shadow: var(--shadow-glass);
  max-width: 400px;
}

.btn-close-error {
  background: none;
  border: none;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: background 0.2s ease;
  margin-left: var(--space-sm);
}

.btn-close-error:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-index-navbar);
    transform: translateX(-100%);
  }

  .chat-sidebar.sidebar-open {
    transform: translateX(0);
  }

  .mobile-header {
    display: flex;
  }

  .messages-container {
    padding: var(--space-md);
  }

  .chat-input-area {
    padding: var(--space-md);
  }

  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-toast {
    top: auto;
    bottom: var(--space-lg);
    left: var(--space-md);
    right: var(--space-md);
  }
}

/* 动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滚动条样式 */
.session-list::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.session-list::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
  background: var(--color-surface-primary);
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
  background: var(--color-surface-secondary);
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}
</style>
