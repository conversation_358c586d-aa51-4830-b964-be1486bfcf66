<template>
  <div class="chat-view">
    <div class="container">
      <div class="chat-placeholder">
        <h1 class="text-xl font-semibold text-primary">聊天功能</h1>
        <p class="text-secondary">聊天功能将在后续模块中实现</p>
        <router-link to="/ai-roles" class="btn btn-primary">
          选择AI角色
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-placeholder {
  text-align: center;
  padding: var(--space-xl);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
}

.chat-placeholder h1 {
  margin-bottom: var(--space-md);
}

.chat-placeholder p {
  margin-bottom: var(--space-lg);
}
</style>
