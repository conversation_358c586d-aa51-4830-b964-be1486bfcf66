<template>
  <div class="chat-view">
    <!-- 会话列表侧边栏 -->
    <div class="session-sidebar" :class="{ 'sidebar-open': showSidebar }">
      <div class="sidebar-header">
        <h2 class="sidebar-title">聊天记录</h2>
        <button class="btn btn-primary btn-sm" @click="showNewChatModal = true">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          新建对话
        </button>
      </div>

      <div class="session-list">
        <!-- 加载状态 -->
        <div v-if="chatStore.isLoading" class="loading-state">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <p class="loading-text">加载中...</p>
        </div>

        <!-- 会话列表 -->
        <div v-else-if="chatStore.sessions.length > 0" class="sessions">
          <div
            v-for="session in chatStore.sessions"
            :key="session.uuid"
            class="session-item"
            :class="{ active: session.uuid === chatStore.currentSessionUuid }"
            @click="selectSession(session.uuid)"
          >
            <div class="session-info">
              <h3 class="session-title">{{ session.title }}</h3>
              <p class="session-role">{{ session.ai_role_name }}</p>
              <span class="session-time">{{ formatTime(session.updated_at) }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-sessions">
          <div class="empty-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="empty-title">还没有聊天记录</h3>
          <p class="empty-description">开始一个新的对话吧</p>
          <button class="btn btn-primary" @click="showNewChatModal = true">
            开始聊天
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩层 -->
    <div
      v-if="showSidebar"
      class="sidebar-overlay"
      @click="showSidebar = false"
    ></div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 移动端顶部栏 -->
      <div class="mobile-header">
        <div class="header-left">
          <button class="back-button" @click="goBack">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button class="sidebar-toggle" @click="showSidebar = !showSidebar">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="current-session-info" v-if="chatStore.currentSession">
          <h3 class="session-title">{{ chatStore.currentSession.title }}</h3>
          <p class="session-role">{{ chatStore.currentSession.ai_role_name }}</p>
        </div>
        <div class="header-right">
          <!-- 占位，保持居中 -->
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" v-if="chatStore.currentSessionUuid">
        <!-- 消息列表 -->
        <div class="messages-container" ref="messagesContainer">
          <div class="messages">
            <div
              v-for="(message, index) in chatStore.messages"
              :key="`${message.created_at}-${index}`"
              class="message"
              :class="[message.role, { streaming: message.streaming, error: message.error }]"
            >
              <div class="message-content">
                <div class="content-text">
                  {{ message.content }}
                  <span v-if="message.streaming" class="typing-indicator">▋</span>
                </div>
                <div class="message-time">{{ formatTime(message.created_at) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <textarea
              v-model="inputMessage"
              ref="messageInput"
              class="message-input"
              placeholder="输入消息..."
              rows="1"
              :disabled="chatStore.isSending"
              @keydown="handleKeyDown"
              @input="adjustTextareaHeight"
            ></textarea>
            <button
              class="send-button"
              :disabled="chatStore.isSending || !inputMessage.trim()"
              @click="sendMessage"
            >
              <svg v-if="!chatStore.isSending" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 2L11 13M22 2L15 22L11 13M22 2L2 9L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <div v-else class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="chat-empty">
        <div class="empty-content">
          <div class="empty-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor" opacity="0.3"/>
              <path d="M12 16L13.09 22.26L22 23L13.09 23.74L12 30L10.91 23.74L2 23L10.91 22.26L12 16Z" fill="currentColor" opacity="0.6"/>
            </svg>
          </div>
          <h2 class="empty-title">选择一个对话开始聊天</h2>
          <p class="empty-description">从左侧选择一个已有的对话，或者创建一个新的对话</p>
          <button class="btn btn-primary" @click="showNewChatModal = true">
            开始新对话
          </button>
        </div>
      </div>
    </div>

    <!-- 新建对话模态框 -->
    <NewChatModal
      :visible="showNewChatModal"
      @close="showNewChatModal = false"
      @chat-created="handleChatCreated"
    />

    <!-- 错误提示 -->
    <div v-if="chatStore.error" class="error-toast">
      {{ chatStore.error }}
      <button @click="chatStore.clearError()" class="error-close">×</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { useChatStore } from '@/stores/chat'
import NewChatModal from '@/components/chat/NewChatModal.vue'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()

// 响应式数据
const inputMessage = ref('')
const showSidebar = ref(false)
const showNewChatModal = ref(false)
const messagesContainer = ref(null)
const messageInput = ref(null)

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }

  // 小于24小时
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }

  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`
  }

  // 显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 选择会话
async function selectSession(sessionUuid) {
  await chatStore.switchSession(sessionUuid)
  showSidebar.value = false // 移动端关闭侧边栏
  scrollToBottom()
}

// 发送消息
async function sendMessage() {
  const content = inputMessage.value.trim()
  if (!content || chatStore.isSending) return

  await chatStore.sendMessage(content)
  inputMessage.value = ''
  adjustTextareaHeight()
  scrollToBottom()
}

// 处理键盘事件
function handleKeyDown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 自动调整文本框高度
function adjustTextareaHeight() {
  const textarea = messageInput.value
  if (textarea) {
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
  }
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 处理新对话创建
function handleChatCreated(session) {
  showNewChatModal.value = false
  chatStore.setCurrentSession(session.uuid)
  scrollToBottom()
}

// 返回上一页
function goBack() {
  // 如果有历史记录，返回上一页
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // 否则返回首页
    router.push({ name: 'home' })
  }
}

// 监听消息变化，自动滚动到底部
watch(() => chatStore.messages.length, () => {
  scrollToBottom()
})

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
    return
  }

  // 加载会话列表
  await chatStore.loadSessions()

  // 处理路由参数
  const sessionId = route.params.sessionId
  if (sessionId) {
    await chatStore.switchSession(sessionId)
  } else if (chatStore.sessions.length > 0) {
    // 如果没有指定会话ID，选择最新的会话
    await chatStore.switchSession(chatStore.sessions[0].uuid)
  }

  // 处理来自AI角色选择的roleId参数
  const roleId = route.query.roleId
  if (roleId && !sessionId) {
    try {
      const newSession = await chatStore.createSession(parseInt(roleId))
      router.replace({ name: 'chat', params: { sessionId: newSession.uuid } })
    } catch (error) {
      console.error('创建会话失败:', error)
    }
  }

  scrollToBottom()
})
</script>

<style scoped>
.chat-view {
  height: 100vh;
  display: flex;
  background: var(--color-background-dark);
  overflow: hidden;
}

/* 会话侧边栏 */
.session-sidebar {
  width: 320px;
  background: var(--color-background-darker);
  border-right: 1px solid var(--color-border-glass);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.session-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-md);
}

.loading-state {
  text-align: center;
  padding: var(--space-xl);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--space-sm);
}

.sessions {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.session-item {
  padding: var(--space-md);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  background: var(--color-surface-primary);
  transform: translateY(-1px);
}

.session-item.active {
  background: var(--color-accent-purple);
  border-color: var(--color-accent-purple-light);
  box-shadow: var(--shadow-glow-purple);
}

.session-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.session-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.session-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.session-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.empty-sessions {
  text-align: center;
  padding: var(--space-xl);
}

.empty-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
}

.empty-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-normal);
}

/* 主聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.mobile-header {
  display: none;
  padding: var(--space-md);
  background: var(--color-background-darker);
  border-bottom: 1px solid var(--color-border-glass);
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header-right {
  width: 60px; /* 与左侧按钮区域保持平衡 */
}

.back-button {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
}

.current-session-info {
  flex: 1;
  text-align: center;
  min-width: 0; /* 允许文本截断 */
}

.current-session-info .session-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.current-session-info .session-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.messages {
  max-width: var(--content-max-width);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.message {
  display: flex;
  animation: fadeInUp 0.3s ease;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.content-text {
  padding: var(--space-md);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  word-wrap: break-word;
  position: relative;
}

.message.user .content-text {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  border-bottom-right-radius: var(--border-radius-sm);
}

.message.assistant .content-text {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  color: var(--color-text-secondary);
  border-bottom-left-radius: var(--border-radius-sm);
}

.message.streaming .content-text {
  border-color: var(--color-accent-purple);
}

.message.error .content-text {
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--color-error);
  color: var(--color-error);
}

.typing-indicator {
  animation: blink 1s infinite;
  color: var(--color-accent-purple);
  font-weight: bold;
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  padding: 0 var(--space-sm);
}

.message.user .message-time {
  text-align: right;
}

/* 输入区域 */
.input-area {
  padding: var(--space-lg);
  background: var(--color-background-darker);
  border-top: 1px solid var(--color-border-glass);
}

.input-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  display: flex;
  gap: var(--space-md);
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: var(--color-background-dark);
  border: 1px solid var(--color-surface-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-md);
  color: var(--color-text-primary);
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  resize: none;
  min-height: 44px;
  max-height: 120px;
  transition: all 0.2s ease;
  outline: none;
}

.message-input:focus {
  border-color: var(--color-accent-purple);
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.message-input::placeholder {
  color: var(--color-text-muted);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  width: 44px;
  height: 44px;
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-yellow);
}

.send-button:active {
  transform: scale(0.98);
}

.send-button:disabled {
  background: var(--color-surface-secondary);
  color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 空状态 */
.chat-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-content .empty-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--space-xl);
}

.empty-content .empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.empty-content .empty-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  line-height: var(--line-height-normal);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-glass);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  z-index: var(--z-index-modal);
  animation: slideInUp 0.3s ease;
}

.error-close {
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* 动画 */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 移动端遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-overlay {
    display: block;
  }
  .session-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
  }

  .session-sidebar.sidebar-open {
    transform: translateX(0);
  }

  .mobile-header {
    display: flex;
  }

  .chat-main {
    width: 100%;
  }

  .message-content {
    max-width: 85%;
  }

  .messages-container {
    padding: var(--space-md);
  }

  .input-area {
    padding: var(--space-md);
  }
}

/* 滚动条样式 */
.session-list::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.session-list::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
  background: var(--color-surface-primary);
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
  background: var(--color-surface-secondary);
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}
</style>
