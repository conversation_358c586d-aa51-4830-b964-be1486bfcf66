<template>
  <div class="password-change-card">
    <div class="card-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="16" r="1" fill="currentColor"/>
            <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="card-title">密码管理</h2>
          <p class="card-subtitle">修改您的登录密码</p>
        </div>
      </div>
    </div>

    <div class="card-content">
      <form @submit.prevent="handleSubmit" class="password-form">
        <!-- 当前密码 -->
        <div class="form-group">
          <label for="current-password" class="form-label">当前密码</label>
          <div class="input-wrapper">
            <input
              id="current-password"
              v-model="formData.currentPassword"
              :type="showCurrentPassword ? 'text' : 'password'"
              class="form-input"
              :class="{ error: errors.currentPassword }"
              placeholder="请输入当前密码"
              :disabled="userStore.isChangingPassword"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showCurrentPassword = !showCurrentPassword"
            >
              <svg v-if="showCurrentPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.68" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.481 9.80385 14.1962C9.51900 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          <span v-if="errors.currentPassword" class="error-message">{{ errors.currentPassword }}</span>
        </div>

        <!-- 新密码 -->
        <div class="form-group">
          <label for="new-password" class="form-label">新密码</label>
          <div class="input-wrapper">
            <input
              id="new-password"
              v-model="formData.newPassword"
              :type="showNewPassword ? 'text' : 'password'"
              class="form-input"
              :class="{ error: errors.newPassword }"
              placeholder="请输入新密码"
              :disabled="userStore.isChangingPassword"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showNewPassword = !showNewPassword"
            >
              <svg v-if="showNewPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.68" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.481 9.80385 14.1962C9.51900 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          <span v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</span>
          <div class="password-requirements">
            <p class="requirements-title">密码要求：</p>
            <ul class="requirements-list">
              <li :class="{ valid: passwordChecks.length }">至少6个字符</li>
              <li :class="{ valid: passwordChecks.hasLetterAndNumber }">包含字母和数字</li>
              <li :class="{ valid: passwordChecks.different }">与当前密码不同</li>
            </ul>
          </div>
        </div>

        <!-- 确认新密码 -->
        <div class="form-group">
          <label for="confirm-password" class="form-label">确认新密码</label>
          <div class="input-wrapper">
            <input
              id="confirm-password"
              v-model="formData.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="form-input"
              :class="{ error: errors.confirmPassword }"
              placeholder="请再次输入新密码"
              :disabled="userStore.isChangingPassword"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showConfirmPassword = !showConfirmPassword"
            >
              <svg v-if="showConfirmPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.68" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.481 9.80385 14.1962C9.51900 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="!isFormValid || userStore.isChangingPassword"
          >
            <span v-if="userStore.isChangingPassword" class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </span>
            <span v-else>修改密码</span>
          </button>
        </div>
      </form>

      <!-- 成功提示 -->
      <div v-if="showSuccess" class="success-message">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.7088 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4905 2.02168 11.3363C2.16356 9.18203 2.99721 7.13214 4.39828 5.49883C5.79935 3.86553 7.69279 2.72636 9.79619 2.24223C11.8996 1.75809 14.1003 1.95185 16.07 2.79" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M22 4L12 14.01L9 11.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        密码修改成功！
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/user'

// 状态管理
const userStore = useUserStore()

// 响应式数据
const formData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const errors = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const showSuccess = ref(false)

// 密码验证
const passwordChecks = computed(() => ({
  length: formData.value.newPassword.length >= 6,
  hasLetterAndNumber: /^(?=.*[a-zA-Z])(?=.*\d)/.test(formData.value.newPassword),
  different: formData.value.newPassword !== formData.value.currentPassword
}))

// 表单验证
const isFormValid = computed(() => {
  return formData.value.currentPassword &&
         formData.value.newPassword &&
         formData.value.confirmPassword &&
         passwordChecks.value.length &&
         passwordChecks.value.hasLetterAndNumber &&
         passwordChecks.value.different &&
         formData.value.newPassword === formData.value.confirmPassword
})

// 监听表单变化，清除错误
watch(() => formData.value.currentPassword, () => {
  if (errors.value.currentPassword) {
    errors.value.currentPassword = ''
  }
})

watch(() => formData.value.newPassword, () => {
  if (errors.value.newPassword) {
    errors.value.newPassword = ''
  }
})

watch(() => formData.value.confirmPassword, () => {
  if (errors.value.confirmPassword) {
    errors.value.confirmPassword = ''
  }
})

// 表单验证
function validateForm() {
  let isValid = true
  
  // 清除之前的错误
  errors.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }

  // 验证当前密码
  if (!formData.value.currentPassword) {
    errors.value.currentPassword = '请输入当前密码'
    isValid = false
  }

  // 验证新密码
  if (!formData.value.newPassword) {
    errors.value.newPassword = '请输入新密码'
    isValid = false
  } else if (!passwordChecks.value.length) {
    errors.value.newPassword = '密码长度至少6个字符'
    isValid = false
  } else if (!passwordChecks.value.hasLetterAndNumber) {
    errors.value.newPassword = '密码必须包含字母和数字'
    isValid = false
  } else if (!passwordChecks.value.different) {
    errors.value.newPassword = '新密码不能与当前密码相同'
    isValid = false
  }

  // 验证确认密码
  if (!formData.value.confirmPassword) {
    errors.value.confirmPassword = '请确认新密码'
    isValid = false
  } else if (formData.value.newPassword !== formData.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  return isValid
}

// 处理表单提交
async function handleSubmit() {
  if (!validateForm()) return

  const success = await userStore.changePassword(
    formData.value.currentPassword,
    formData.value.newPassword
  )

  if (success) {
    // 重置表单
    formData.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    
    // 显示成功提示
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 3000)
  }
}
</script>

<style scoped>
.password-change-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all 0.3s ease;
}

.password-change-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

/* 卡片头部 */
.card-header {
  background: var(--color-surface-primary);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--color-accent-yellow);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-background-dark);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 卡片内容 */
.card-content {
  padding: var(--space-lg);
}

/* 表单样式 */
.password-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  padding-right: 48px;
  background: var(--color-background-dark);
  border: 1px solid var(--color-surface-secondary);
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  outline: none;
}

.form-input:focus {
  border-color: var(--color-accent-purple);
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.form-input.error {
  border-color: var(--color-error);
}

.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-toggle {
  position: absolute;
  right: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--color-text-secondary);
}

.error-message {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: var(--space-xs);
}

/* 密码要求 */
.password-requirements {
  background: var(--color-surface-secondary);
  padding: var(--space-md);
  border-radius: var(--border-radius-sm);
  border-left: 4px solid var(--color-accent-purple);
}

.requirements-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.requirements-list li {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  position: relative;
  padding-left: 20px;
  transition: color 0.2s ease;
}

.requirements-list li::before {
  content: '✗';
  position: absolute;
  left: 0;
  color: var(--color-error);
  transition: color 0.2s ease;
}

.requirements-list li.valid {
  color: #4CAF50;
}

.requirements-list li.valid::before {
  content: '✓';
  color: #4CAF50;
}

/* 表单操作 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--space-md);
}

/* 成功消息 */
.success-message {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: var(--border-radius-md);
  padding: var(--space-md);
  color: #4CAF50;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
  animation: slideInUp 0.3s ease;
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-actions {
    justify-content: stretch;
  }

  .form-actions .btn {
    width: 100%;
  }
}
</style>
