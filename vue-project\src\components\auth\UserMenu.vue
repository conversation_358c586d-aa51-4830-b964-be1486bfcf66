<template>
  <div class="user-menu">
    <!-- 未认证状态 -->
    <div v-if="!authStore.isAuthenticated" class="auth-buttons">
      <button 
        class="btn btn-secondary btn-sm"
        @click="showAuthModal('login')"
      >
        登录
      </button>
      <button 
        class="btn btn-primary btn-sm"
        @click="showAuthModal('register')"
      >
        注册
      </button>
    </div>

    <!-- 已认证状态 -->
    <div v-else class="user-info">
      <div class="user-avatar" @click="toggleDropdown">
        <div class="avatar-circle">
          <span class="avatar-text">
            {{ getUserInitial() }}
          </span>
        </div>
        <div class="user-status">
          <span class="status-badge" :class="{ 'guest': authStore.isGuest }">
            {{ authStore.isGuest ? '游客' : '用户' }}
          </span>
        </div>
      </div>

      <!-- 下拉菜单 -->
      <Teleport to="body">
        <div v-if="showDropdown" class="dropdown-overlay" @click="closeDropdown">
          <div class="user-dropdown" @click.stop :style="dropdownStyle">
        <div class="dropdown-header">
          <div class="user-details">
            <span class="user-email" v-if="!authStore.isGuest">
              {{ authStore.user?.email }}
            </span>
            <span class="user-type">
              {{ authStore.isGuest ? '游客模式' : '注册用户' }}
            </span>
          </div>
        </div>

        <div class="dropdown-divider"></div>

        <div class="dropdown-menu">
          <!-- 游客用户选项 -->
          <template v-if="authStore.isGuest">
            <button 
              class="dropdown-item"
              @click="showAuthModal('register')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              注册账户
            </button>
            <button 
              class="dropdown-item"
              @click="showAuthModal('login')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 17L15 12L10 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              已有账户
            </button>
          </template>

          <!-- 注册用户选项 -->
          <template v-else>
            <button
              class="dropdown-item"
              @click="$router.push('/profile')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              个人中心
            </button>
            <button
              class="dropdown-item"
              @click="$router.push('/account')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
              我的账户
            </button>
            <button 
              class="dropdown-item"
              @click="$router.push('/history')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              聊天历史
            </button>
          </template>

          <div class="dropdown-divider"></div>

          <button 
            class="dropdown-item logout"
            @click="handleLogout"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17L21 12L16 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            退出
          </button>
        </div>
          </div>
        </div>
      </Teleport>
    </div>

    <!-- 认证模态框 -->
    <AuthModal
      :visible="authModalVisible"
      :initial-view="authModalView"
      @close="authModalVisible = false"
      @auth-success="handleAuthSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import AuthModal from './AuthModal.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const showDropdown = ref(false)
const authModalVisible = ref(false)
const authModalView = ref('guest')
const dropdownStyle = ref({})

// 获取用户头像首字母
function getUserInitial() {
  if (authStore.user?.email) {
    return authStore.user.email.charAt(0).toUpperCase()
  }
  return '游'
}

// 切换下拉菜单
function toggleDropdown(event) {
  showDropdown.value = !showDropdown.value

  if (showDropdown.value) {
    nextTick(() => {
      calculateDropdownPosition(event.currentTarget)
    })
  }
}

// 关闭下拉菜单
function closeDropdown() {
  showDropdown.value = false
}

// 计算下拉菜单位置
function calculateDropdownPosition(triggerElement) {
  if (!triggerElement) return

  const rect = triggerElement.getBoundingClientRect()
  const dropdownWidth = 220
  const dropdownHeight = 200 // 估算高度

  let left = rect.right - dropdownWidth
  let top = rect.bottom + 8

  // 检查右边界
  if (left < 8) {
    left = 8
  }

  // 检查左边界
  if (left + dropdownWidth > window.innerWidth - 8) {
    left = window.innerWidth - dropdownWidth - 8
  }

  // 检查底部边界
  if (top + dropdownHeight > window.innerHeight - 8) {
    top = rect.top - dropdownHeight - 8
  }

  dropdownStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    zIndex: 9999
  }
}

// 显示认证模态框
function showAuthModal(view = 'guest') {
  authModalView.value = view
  authModalVisible.value = true
  showDropdown.value = false
}

// 处理认证成功
function handleAuthSuccess() {
  authModalVisible.value = false
}

// 处理退出登录
function handleLogout() {
  authStore.logout()
  showDropdown.value = false
  router.push('/')
}

// 生命周期钩子（如果需要的话）
onMounted(() => {
  // 初始化逻辑
})

onUnmounted(() => {
  // 清理逻辑
})
</script>

<style scoped>
.user-menu {
  position: relative;
}

.auth-buttons {
  display: flex;
  gap: var(--space-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-xs);
}

.user-info {
  position: relative;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.user-avatar:hover {
  background: var(--color-surface-primary);
}

.avatar-circle {
  width: 32px;
  height: 32px;
  background: var(--color-accent-purple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.user-status {
  display: flex;
  flex-direction: column;
}

.status-badge {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  padding: 2px 6px;
  background: var(--color-surface-primary);
  border-radius: var(--border-radius-sm);
}

.status-badge.guest {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
}

/* 下拉菜单覆盖层 */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

.user-dropdown {
  min-width: 220px;
  background: var(--color-background-dark);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  animation: fadeInUp 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-header {
  padding: var(--space-md);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.user-email {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.user-type {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.dropdown-divider {
  height: 1px;
  background: var(--color-border-glass);
  margin: var(--space-xs) 0;
}

.dropdown-menu {
  padding: var(--space-xs);
}

.dropdown-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  text-align: left;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
}

.dropdown-item.logout {
  color: var(--color-error);
}

.dropdown-item.logout:hover {
  background: rgba(244, 67, 54, 0.1);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .user-dropdown {
    right: auto;
    left: 0;
    min-width: 180px;
  }
}
</style>
