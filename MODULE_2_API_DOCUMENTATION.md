# AI伙伴聊天应用 - 模块二API文档

## 概述

本文档描述了AI伙伴聊天应用**模块二：AI 角色展示与选择**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: 本模块所有接口均为公开接口，无需认证

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "AI Role not found",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 400 | 40001 | 请求参数无效 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |

---

## 接口列表

### 1. 获取AI角色列表

**接口描述**: 获取所有已启用的AI角色列表，用于角色选择页面的卡片展示

- **URL**: `GET /ai-roles`
- **认证**: 无需认证
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": 1,
      "name": "博学的历史学家",
      "avatar_url": "https://example.com/avatars/historian.png",
      "description": "精通世界历史，能与你深入探讨任何历史事件。"
    },
    {
      "id": 2,
      "name": "温柔的心理顾问",
      "avatar_url": "https://example.com/avatars/counselor.png",
      "description": "倾听你的烦恼，给予你温暖的支持和专业的建议。"
    },
    {
      "id": 3,
      "name": "活泼的生活助手",
      "avatar_url": "https://example.com/avatars/assistant.png",
      "description": "帮你解决生活中的各种问题，让每一天都充满活力。"
    },
    {
      "id": 4,
      "name": "理性的分析师",
      "avatar_url": "https://example.com/avatars/analyst.png",
      "description": "用逻辑和数据帮你分析问题，找到最优解决方案。"
    }
  ]
}
```

**字段说明**:
- `id`: AI角色的唯一标识符
- `name`: 角色名称
- `avatar_url`: 角色头像图片URL
- `description`: 角色的简短介绍，用于卡片展示

**特点**:
- 返回结果按ID升序排列
- 只返回已启用的角色（is_active = true）
- 不包含敏感信息（如system_prompt）

### 2. 获取AI角色详情

**接口描述**: 获取指定AI角色的详细信息，用于角色详情页面展示

- **URL**: `GET /ai-roles/{id}`
- **认证**: 无需认证

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | AI角色的ID，必须是正整数 |

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 1,
    "name": "博学的历史学家",
    "avatar_url": "https://example.com/avatars/historian.png",
    "description": "精通世界历史，能与你深入探讨任何历史事件。",
    "details": "他是一位沉浸在时间长河中的学者，书房里堆满了古籍和地图。他不仅知道重大事件的始末，更了解那些不为人知的历史细节。无论是古代文明的兴衰，还是近现代的变革，他都能为你娓娓道来，让历史变得生动有趣。",
    "system_prompt": "你是一位博学的历史学家，拥有深厚的历史知识和丰富的学术经验。你善于用生动有趣的方式讲述历史事件，能够将复杂的历史背景用通俗易懂的语言解释清楚。你对各个时代的文化、政治、经济都有深入的了解，总是能够提供独特的历史视角和深刻的见解。",
    "created_at": "2025-07-22T10:00:00.000Z",
    "updated_at": "2025-07-22T10:00:00.000Z"
  }
}
```

**字段说明**:
- `id`: AI角色的唯一标识符
- `name`: 角色名称
- `avatar_url`: 角色头像图片URL
- `description`: 角色的简短介绍
- `details`: 角色的详细背景故事和设定
- `system_prompt`: 核心人设指令（用于AI对话）
- `created_at`: 角色创建时间
- `updated_at`: 角色最后更新时间

**错误响应**:

**404 Not Found** - 角色不存在:
```json
{
  "code": 40401,
  "message": "AI Role not found",
  "data": null
}
```

**400 Bad Request** - 参数无效:
```json
{
  "code": 40001,
  "message": "AI角色ID必须是正数",
  "data": null
}
```

---

## 参数验证规则

### AI角色ID验证
- **类型**: 整数
- **范围**: 必须是正数（> 0）
- **格式**: 不接受小数、负数、零或非数字字符

**无效示例**:
- `/ai-roles/0` - 零不被接受
- `/ai-roles/-1` - 负数不被接受
- `/ai-roles/1.5` - 小数不被接受
- `/ai-roles/abc` - 非数字不被接受

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 获取AI角色列表
async function getAiRoles() {
  try {
    const response = await fetch('/api/v1/ai-roles');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('AI角色列表:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 获取指定AI角色详情
async function getAiRoleDetail(roleId) {
  try {
    const response = await fetch(`/api/v1/ai-roles/${roleId}`);
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('AI角色详情:', result.data);
      return result.data;
    } else if (result.code === 40401) {
      console.error('角色不存在');
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 使用示例
getAiRoles().then(roles => {
  if (roles && roles.length > 0) {
    // 获取第一个角色的详情
    getAiRoleDetail(roles[0].id);
  }
});
```

### Vue.js 组件示例

```vue
<template>
  <div class="ai-roles">
    <!-- 角色列表 -->
    <div class="role-grid">
      <div 
        v-for="role in roles" 
        :key="role.id"
        class="role-card"
        @click="selectRole(role.id)"
      >
        <img :src="role.avatar_url" :alt="role.name" />
        <h3>{{ role.name }}</h3>
        <p>{{ role.description }}</p>
      </div>
    </div>
    
    <!-- 角色详情弹窗 -->
    <div v-if="selectedRole" class="role-detail-modal">
      <h2>{{ selectedRole.name }}</h2>
      <img :src="selectedRole.avatar_url" :alt="selectedRole.name" />
      <p>{{ selectedRole.details }}</p>
      <button @click="startChat(selectedRole.id)">开始聊天</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      roles: [],
      selectedRole: null
    }
  },
  
  async mounted() {
    await this.loadRoles();
  },
  
  methods: {
    async loadRoles() {
      try {
        const response = await fetch('/api/v1/ai-roles');
        const result = await response.json();
        if (result.code === 200) {
          this.roles = result.data;
        }
      } catch (error) {
        console.error('加载角色列表失败:', error);
      }
    },
    
    async selectRole(roleId) {
      try {
        const response = await fetch(`/api/v1/ai-roles/${roleId}`);
        const result = await response.json();
        if (result.code === 200) {
          this.selectedRole = result.data;
        }
      } catch (error) {
        console.error('加载角色详情失败:', error);
      }
    },
    
    startChat(roleId) {
      // 跳转到聊天页面，传递角色ID
      this.$router.push(`/chat?roleId=${roleId}`);
    }
  }
}
</script>
```

---

## 数据库结构

### ai_roles表结构

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INT UNSIGNED | PRIMARY KEY, AUTO_INCREMENT | 角色主键 |
| name | VARCHAR(100) | NOT NULL | 角色名称 |
| avatar_url | VARCHAR(255) | NULL | 角色头像URL |
| description | VARCHAR(500) | NULL | 角色简短介绍 |
| details | TEXT | NULL | 角色详细背景 |
| system_prompt | TEXT | NOT NULL | AI人设指令 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否启用 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

---

## 注意事项

1. **缓存建议**: 角色列表数据相对稳定，建议前端进行适当缓存
2. **图片处理**: avatar_url可能为空，前端需要提供默认头像
3. **文本长度**: description和details字段可能较长，注意UI布局
4. **错误处理**: 务必处理网络错误和API错误响应
5. **性能优化**: 角色列表接口响应较快，详情接口包含更多数据

---

## 测试覆盖

模块二包含完整的测试覆盖：
- ✅ 角色列表获取测试
- ✅ 角色详情获取测试  
- ✅ 参数验证测试（无效ID、负数、小数等）
- ✅ 错误处理测试（404、400等）
- ✅ 数据一致性测试
- ✅ 排序验证测试

所有测试用例均已通过验证。
