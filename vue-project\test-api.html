<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>模块一 API 测试</h1>
    
    <div class="test-section">
        <h2>1. 创建游客账户</h2>
        <button onclick="testCreateGuest()">测试创建游客</button>
        <div id="guest-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 用户注册</h2>
        <input type="email" id="register-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="register-password" placeholder="密码" value="password123">
        <button onclick="testRegister()">测试注册</button>
        <div id="register-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 用户登录</h2>
        <input type="email" id="login-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="login-password" placeholder="密码" value="password123">
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 获取用户信息</h2>
        <input type="text" id="token-input" placeholder="JWT Token">
        <button onclick="testGetUser()">测试获取用户信息</button>
        <div id="user-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testCreateGuest() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/guest`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.data?.token || '';
                    document.getElementById('token-input').value = currentToken;
                    displayResult('guest-result', data);
                } else {
                    displayResult('guest-result', data, true);
                }
            } catch (error) {
                displayResult('guest-result', { error: error.message }, true);
            }
        }

        async function testRegister() {
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.data?.token || '';
                    document.getElementById('token-input').value = currentToken;
                    displayResult('register-result', data);
                } else {
                    displayResult('register-result', data, true);
                }
            } catch (error) {
                displayResult('register-result', { error: error.message }, true);
            }
        }

        async function testLogin() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.data?.token || '';
                    document.getElementById('token-input').value = currentToken;
                    displayResult('login-result', data);
                } else {
                    displayResult('login-result', data, true);
                }
            } catch (error) {
                displayResult('login-result', { error: error.message }, true);
            }
        }

        async function testGetUser() {
            const token = document.getElementById('token-input').value || currentToken;
            
            if (!token) {
                displayResult('user-result', { error: '请先获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    displayResult('user-result', data);
                } else {
                    displayResult('user-result', data, true);
                }
            } catch (error) {
                displayResult('user-result', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
