import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)
  const isLoading = ref(false)
  const error = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isGuest = computed(() => {
    if (!user.value) return false
    // 兼容后端返回的数字类型和布尔类型
    return user.value.is_guest === true || user.value.is_guest === 1
  })
  const isRegisteredUser = computed(() => {
    if (!user.value) return false
    // 兼容后端返回的数字类型和布尔类型
    return user.value.is_guest === false || user.value.is_guest === 0
  })

  // 方法
  function setUser(userData) {
    user.value = userData
  }

  function setToken(tokenValue) {
    token.value = tokenValue
    if (tokenValue) {
      localStorage.setItem('token', tokenValue)
    } else {
      localStorage.removeItem('token')
    }
  }

  function setLoading(loading) {
    isLoading.value = loading
  }

  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = null
  }

  function logout() {
    user.value = null
    setToken(null)
    clearError()
  }

  function updateUserInfo(updates) {
    if (user.value) {
      user.value = { ...user.value, ...updates }
    }
  }

  // 初始化时从localStorage恢复用户信息
  function initializeAuth() {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (e) {
        console.error('Failed to parse saved user data:', e)
        logout()
      }
    }
  }

  // 保存用户信息到localStorage
  function saveUserToStorage() {
    if (user.value) {
      localStorage.setItem('user', JSON.stringify(user.value))
    } else {
      localStorage.removeItem('user')
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    error,

    // 计算属性
    isAuthenticated,
    isGuest,
    isRegisteredUser,

    // 方法
    setUser,
    setToken,
    setLoading,
    setError,
    clearError,
    logout,
    updateUserInfo,
    initializeAuth,
    saveUserToStorage
  }
})
