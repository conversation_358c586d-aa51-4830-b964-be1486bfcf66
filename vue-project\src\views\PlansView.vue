<template>
  <div class="plans-view">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">定价</h1>
        <p class="page-description">选择适合您的套餐</p>

        <!-- 计费周期切换 -->
        <div class="billing-toggle">
          <div class="toggle-container">
            <button
              class="toggle-option"
              :class="{ active: billingCycle === 'monthly' }"
              @click="billingCycle = 'monthly'"
            >
              按月付费
            </button>
            <button
              class="toggle-option"
              :class="{ active: billingCycle === 'yearly' }"
              @click="billingCycle = 'yearly'"
            >
              按年付费
              <span class="discount-badge">省20%</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="paymentStore.isLoading && !paymentStore.hasPlans" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载套餐信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="paymentStore.error && !paymentStore.hasPlans" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ paymentStore.error }}</p>
        <button class="btn btn-primary" @click="paymentStore.refreshData()">
          重试
        </button>
      </div>

      <!-- 主要内容 -->
      <div v-else class="plans-content">
        <!-- 个人套餐 -->
        <div class="plans-section">
          <div class="section-header">
            <h2 class="section-title">个人套餐</h2>
          </div>
          <div class="plans-grid">
            <!-- 免费套餐 -->
            <div class="plan-card free-plan">
              <div class="plan-header">
                <h3 class="plan-name">免费体验</h3>
                <div class="plan-price">
                  <span class="price-amount">免费</span>
                </div>
              </div>
              <div class="plan-description">
                <p>体验AI聊天的基础功能</p>
              </div>
              <div class="plan-features">
                <div class="feature-item">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <span>每日10条消息</span>
                </div>
                <div class="feature-item">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <span>基础AI角色</span>
                </div>
              </div>
              <button
                class="plan-button secondary"
                @click="handleFreePlan"
              >
                立即开始
              </button>
            </div>

            <!-- 付费套餐 -->
            <PlanCard
              v-for="plan in paymentStore.subscriptionPlans"
              :key="plan.id"
              :plan="plan"
              :billing-cycle="billingCycle"
              :selected="paymentStore.selectedPlan?.id === plan.id"
              @select="handlePlanSelect(plan)"
            />
          </div>
        </div>

        <!-- 消息包套餐 -->
        <div v-if="paymentStore.oneTimePlans.length > 0" class="plans-section">
          <div class="section-header">
            <h2 class="section-title">消息包</h2>
            <p class="section-description">一次性购买，按需使用</p>
          </div>
          <div class="plans-grid message-packs">
            <PlanCard
              v-for="plan in paymentStore.oneTimePlans"
              :key="plan.id"
              :plan="plan"
              :selected="paymentStore.selectedPlan?.id === plan.id"
              @select="handlePlanSelect(plan)"
            />
          </div>
        </div>


      </div>

      <!-- 支付弹窗 -->
      <PaymentModal />

      <!-- 全局错误提示 -->
      <div v-if="paymentStore.error" class="error-toast">
        {{ paymentStore.error }}
        <button @click="paymentStore.clearError()" class="error-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { usePaymentStore } from '@/stores/payment'
import PlanCard from '@/components/payment/PlanCard.vue'
import PaymentModal from '@/components/payment/PaymentModal.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()
const paymentStore = usePaymentStore()

// 响应式数据
const billingCycle = ref('monthly')

// 处理免费套餐
function handleFreePlan() {
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
  } else {
    router.push('/ai-roles')
  }
}

// 处理套餐选择
async function handlePlanSelect(plan) {
  paymentStore.setSelectedPlan(plan)

  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
    return
  }

  // 直接创建订单
  await paymentStore.createOrder(plan.id)
}

// 组件挂载时的初始化
onMounted(async () => {
  // 加载套餐数据
  await paymentStore.loadPlans()

  // 如果用户已认证，加载订阅和订单数据
  if (authStore.isAuthenticated) {
    await Promise.all([
      paymentStore.loadSubscriptions(),
      paymentStore.loadOrders()
    ])
  }
})
</script>

<style scoped>
.plans-view {
  min-height: 100vh;
  background: var(--color-background-dark);
  padding: var(--space-xl) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  width: 100%;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-xl);
}

/* 计费周期切换 */
.billing-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-xl);
}

.toggle-container {
  display: flex;
  background: var(--color-surface-secondary);
  border-radius: var(--border-radius-lg);
  padding: 4px;
  border: 1px solid var(--color-border-glass);
}

.toggle-option {
  position: relative;
  background: none;
  border: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.toggle-option.active {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-glass);
}

.discount-badge {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 主要内容 */
.plans-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3xl);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 套餐区块 */
.plans-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.section-header {
  text-align: center;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
}

.section-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-xl);
  max-width: 1000px;
  margin: 0 auto;
  justify-items: center;
}

.plans-grid.message-packs {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  max-width: 900px;
  justify-items: center;
}

/* 免费套餐卡片 */
.plan-card.free-plan {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 2px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.plan-card.free-plan:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
  border-color: var(--color-accent-purple);
}

.plan-header {
  text-align: center;
  margin-bottom: var(--space-lg);
}

.plan-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-xs);
}

.price-amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-yellow);
  line-height: 1;
}

.plan-description {
  margin-bottom: var(--space-lg);
  text-align: center;
}

.plan-description p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
  flex: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.feature-item svg {
  color: var(--color-accent-yellow);
  flex-shrink: 0;
}

.plan-button {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: auto;
}

.plan-button.secondary {
  background: var(--color-surface-secondary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-glass);
}

.plan-button.secondary:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-purple);
  transform: translateY(-1px);
}

/* 购买区块 */
.purchase-section {
  text-align: center;
  margin-top: var(--space-xl);
}

.purchase-btn {
  min-width: 280px;
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
}

.purchase-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.purchase-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-yellow);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-glass);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  z-index: var(--z-index-modal);
  animation: slideInUp 0.3s ease;
}

.error-close {
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .plans-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }
}

@media (max-width: 768px) {
  .plans-view {
    padding: var(--space-lg) 0;
  }

  .container {
    padding: 0 var(--space-md);
    max-width: none;
  }

  .page-header {
    margin-bottom: var(--space-xl);
  }

  .page-title {
    font-size: 2rem;
  }

  .page-description {
    font-size: var(--font-size-base);
  }

  .toggle-container {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .toggle-option {
    justify-content: center;
  }

  .plans-content {
    gap: var(--space-xl);
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    max-width: none;
  }

  .plans-grid.message-packs {
    grid-template-columns: 1fr;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-sm);
  }

  .page-title {
    font-size: 1.75rem;
  }

  .plans-grid {
    gap: var(--space-md);
  }

  .plan-card,
  .plan-card.free-plan {
    padding: var(--space-lg);
  }
}
</style>
