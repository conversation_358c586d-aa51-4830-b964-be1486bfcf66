<template>
  <div class="plans-view">
    <div class="container">
      <div class="plans-placeholder">
        <h1 class="text-xl font-semibold text-primary">套餐选择</h1>
        <p class="text-secondary">套餐选择功能将在后续模块中实现</p>
        <router-link to="/" class="btn btn-primary">
          返回首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.plans-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plans-placeholder {
  text-align: center;
  padding: var(--space-xl);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
}

.plans-placeholder h1 {
  margin-bottom: var(--space-md);
}

.plans-placeholder p {
  margin-bottom: var(--space-lg);
}
</style>
