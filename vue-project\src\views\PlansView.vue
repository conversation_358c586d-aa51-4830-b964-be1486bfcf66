<template>
  <div class="plans-view">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">套餐选择</h1>
        <p class="page-description">选择适合您的套餐，享受更好的AI聊天体验</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="paymentStore.isLoading && !paymentStore.hasPlans" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载套餐信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="paymentStore.error && !paymentStore.hasPlans" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ paymentStore.error }}</p>
        <button class="btn btn-primary" @click="paymentStore.refreshData()">
          重试
        </button>
      </div>

      <!-- 主要内容 -->
      <div v-else class="plans-content">
        <!-- 订阅套餐 -->
        <div v-if="paymentStore.subscriptionPlans.length > 0" class="plans-section">
          <div class="section-header">
            <h2 class="section-title">会员订阅</h2>
            <p class="section-description">无限畅聊，解锁所有高级功能</p>
          </div>
          <div class="plans-grid">
            <PlanCard
              v-for="plan in paymentStore.subscriptionPlans"
              :key="plan.id"
              :plan="plan"
              :selected="paymentStore.selectedPlan?.id === plan.id"
              @select="paymentStore.setSelectedPlan(plan)"
            />
          </div>
        </div>

        <!-- 消息包套餐 -->
        <div v-if="paymentStore.oneTimePlans.length > 0" class="plans-section">
          <div class="section-header">
            <h2 class="section-title">消息包</h2>
            <p class="section-description">一次性购买，按需使用</p>
          </div>
          <div class="plans-grid">
            <PlanCard
              v-for="plan in paymentStore.oneTimePlans"
              :key="plan.id"
              :plan="plan"
              :selected="paymentStore.selectedPlan?.id === plan.id"
              @select="paymentStore.setSelectedPlan(plan)"
            />
          </div>
        </div>

        <!-- 购买按钮 -->
        <div class="purchase-section">
          <button
            class="btn btn-primary btn-lg purchase-btn"
            :disabled="!paymentStore.selectedPlan || paymentStore.isCreatingOrder"
            @click="handlePurchase"
          >
            <span v-if="paymentStore.isCreatingOrder" class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </span>
            <span v-else>
              {{ paymentStore.selectedPlan ? `立即购买 ${paymentStore.formatPrice(paymentStore.selectedPlan.price)}` : '请选择套餐' }}
            </span>
          </button>
        </div>

        <!-- 用户订阅状态 -->
        <UserSubscriptionStatus v-if="authStore.isAuthenticated" />

        <!-- 订单历史 -->
        <UserOrderHistory v-if="authStore.isAuthenticated" />
      </div>

      <!-- 支付弹窗 -->
      <PaymentModal />

      <!-- 全局错误提示 -->
      <div v-if="paymentStore.error" class="error-toast">
        {{ paymentStore.error }}
        <button @click="paymentStore.clearError()" class="error-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { usePaymentStore } from '@/stores/payment'
import PlanCard from '@/components/payment/PlanCard.vue'
import UserSubscriptionStatus from '@/components/payment/UserSubscriptionStatus.vue'
import UserOrderHistory from '@/components/payment/UserOrderHistory.vue'
import PaymentModal from '@/components/payment/PaymentModal.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()
const paymentStore = usePaymentStore()

// 处理购买
async function handlePurchase() {
  if (!paymentStore.selectedPlan) return

  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
    return
  }

  // 创建订单
  await paymentStore.createOrder(paymentStore.selectedPlan.id)
}

// 组件挂载时的初始化
onMounted(async () => {
  // 加载套餐数据
  await paymentStore.loadPlans()

  // 如果用户已认证，加载订阅和订单数据
  if (authStore.isAuthenticated) {
    await Promise.all([
      paymentStore.loadSubscriptions(),
      paymentStore.loadOrders()
    ])
  }
})
</script>

<style scoped>
.plans-view {
  min-height: 100vh;
  background: var(--color-background-dark);
  padding: var(--space-xl) 0;
}

.container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 主要内容 */
.plans-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3xl);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 套餐区块 */
.plans-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.section-header {
  text-align: center;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
}

.section-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  max-width: 1000px;
  margin: 0 auto;
}

/* 购买区块 */
.purchase-section {
  text-align: center;
  margin-top: var(--space-xl);
}

.purchase-btn {
  min-width: 280px;
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
}

.purchase-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.purchase-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-yellow);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-glass);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  z-index: var(--z-index-modal);
  animation: slideInUp 0.3s ease;
}

.error-close {
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plans-view {
    padding: var(--space-lg) 0;
  }

  .container {
    padding: 0 var(--space-md);
  }

  .page-header {
    margin-bottom: var(--space-xl);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .plans-content {
    gap: var(--space-xl);
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .purchase-btn {
    width: 100%;
    min-width: auto;
  }
}
</style>
