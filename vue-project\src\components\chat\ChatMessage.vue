<template>
  <div class="chat-message" :class="messageClass">
    <!-- 用户消息 -->
    <div v-if="message.role === 'user'" class="user-message">
      <div class="message-content">
        <div class="message-text">{{ message.content }}</div>
        <div class="message-time">{{ formatTime(message.created_at) }}</div>
      </div>
      <div class="user-avatar">
        <div class="avatar-circle">
          <span class="avatar-text">{{ getUserInitial() }}</span>
        </div>
      </div>
    </div>

    <!-- AI消息 -->
    <div v-else class="ai-message">
      <div class="ai-avatar">
        <img 
          v-if="aiRole?.avatar_url" 
          :src="aiRole.avatar_url" 
          :alt="aiRole.name"
          class="avatar-image"
          @error="handleImageError"
        />
        <div v-else class="avatar-placeholder">
          <span class="avatar-initial">{{ aiRole?.name?.charAt(0) || 'AI' }}</span>
        </div>
      </div>
      <div class="message-content">
        <div class="message-header">
          <span class="ai-name">{{ aiRole?.name || 'AI助手' }}</span>
          <div class="message-time">{{ formatTime(message.created_at) }}</div>
        </div>
        <div class="message-text" :class="{ 'message-streaming': message.streaming, 'message-error': message.error }">
          <span v-if="message.streaming && !message.content" class="typing-indicator">
            <span class="typing-dot"></span>
            <span class="typing-dot"></span>
            <span class="typing-dot"></span>
          </span>
          <span v-else>{{ message.content }}</span>
        </div>
        <div v-if="message.error" class="message-error-indicator">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>发送失败</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/counter'

// 定义props
const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  aiRole: {
    type: Object,
    default: null
  }
})

// 状态管理
const authStore = useAuthStore()

// 计算属性
const messageClass = computed(() => ({
  'message-user': props.message.role === 'user',
  'message-ai': props.message.role === 'assistant',
  'message-streaming': props.message.streaming,
  'message-error': props.message.error
}))

// 获取用户头像首字母
function getUserInitial() {
  if (authStore.user?.email) {
    return authStore.user.email.charAt(0).toUpperCase()
  }
  return authStore.isGuest ? '游' : 'U'
}

// 格式化时间
function formatTime(dateString) {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 处理图片加载错误
function handleImageError(event) {
  if (event.target) {
    event.target.style.display = 'none'
    const placeholder = event.target.parentElement?.querySelector('.avatar-placeholder')
    if (placeholder) {
      placeholder.style.display = 'flex'
    }
  }
}
</script>

<style scoped>
.chat-message {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-message {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.ai-message {
  display: flex;
  justify-content: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.message-content {
  max-width: 70%;
  min-width: 120px;
}

.user-message .message-content {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-sm) var(--border-radius-lg);
  box-shadow: var(--shadow-glow-purple);
}

.ai-message .message-content {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  color: var(--color-text-secondary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-sm);
  box-shadow: var(--shadow-glass);
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-sm);
}

.ai-name {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-accent-purple-light);
}

.message-text {
  line-height: var(--line-height-normal);
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-streaming {
  opacity: 0.8;
}

.message-error {
  color: var(--color-error);
}

.message-time {
  font-size: var(--font-size-xs);
  opacity: 0.7;
  white-space: nowrap;
}

.user-message .message-time {
  margin-top: var(--space-xs);
  text-align: right;
}

.user-avatar, .ai-avatar {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  position: relative;
}

.avatar-circle {
  width: 100%;
  height: 100%;
  background: var(--color-accent-yellow);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-border-glass);
}

.avatar-text {
  color: var(--color-background-dark);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-border-glass);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-border-glass);
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: var(--color-accent-purple);
  border-radius: 50%;
  animation: typingPulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.message-error-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
  color: var(--color-error);
  font-size: var(--font-size-xs);
}

/* 动画 */
@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }
  
  .user-message .message-content,
  .ai-message .message-content {
    padding: var(--space-sm) var(--space-md);
  }
  
  .user-avatar, .ai-avatar {
    width: 32px;
    height: 32px;
  }
  
  .avatar-text, .avatar-initial {
    font-size: var(--font-size-xs);
  }
}
</style>
