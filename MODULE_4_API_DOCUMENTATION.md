# AI伙伴聊天应用 - 模块四API文档

## 概述

本文档描述了AI伙伴聊天应用**模块四：用户中心**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（所有接口都需要认证）

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "用户不存在",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 403 | 40301 | 禁止访问，权限不足 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |

---

## 接口列表

### 1. 获取当前用户信息

**接口描述**: 获取当前已登录用户的详细信息

- **URL**: `GET /me`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
    "email": "<EMAIL>",
    "is_guest": false,
    "status": "active",
    "daily_message_count": 5,
    "message_credits": 100,
    "profile_summary": "该用户对古罗马历史表现出浓厚兴趣。",
    "created_at": "2025-07-20T10:00:00.000Z"
  }
}
```

**字段说明**:
- `uuid`: 用户的唯一标识符
- `email`: 用户邮箱（游客用户为null）
- `is_guest`: 是否为游客用户
- `status`: 账户状态（active/banned）
- `daily_message_count`: 今日已使用的消息次数
- `message_credits`: 剩余消息点数
- `profile_summary`: 用户画像摘要（AI分析生成）
- `created_at`: 账户创建时间

**错误响应**:
- `401 Unauthorized`: Token无效或已过期
- `404 Not Found`: 用户不存在

### 2. 获取用户的聊天会话列表

**接口描述**: 获取当前用户的所有历史聊天会话列表，按最后更新时间倒序排列

- **URL**: `GET /me/chats`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
      "title": "新的对话",
      "ai_role_id": 1,
      "ai_role_name": "博学的历史学家",
      "updated_at": "2025-07-21T23:24:00.000Z"
    },
    {
      "uuid": "d4e5f6a7-b8c9-0123-4567-890abcdef123",
      "title": "新的对话",
      "ai_role_id": 2,
      "ai_role_name": "温柔的心理顾问",
      "updated_at": "2025-07-20T18:30:00.000Z"
    }
  ]
}
```

**字段说明**:
- `uuid`: 会话的唯一标识符
- `title`: 会话标题
- `ai_role_id`: 关联的AI角色ID
- `ai_role_name`: AI角色名称
- `updated_at`: 最后更新时间

**特点**:
- 按最后更新时间倒序排列（最新的在前）
- 包含AI角色信息便于前端显示
- 支持游客和注册用户

**错误响应**:
- `401 Unauthorized`: Token无效或已过期

### 3. 修改密码

**接口描述**: 修改当前用户的登录密码

- **URL**: `PUT /me/password`
- **认证**: 需要JWT Token

**请求参数**:
```json
{
  "old_password": "oldpassword123",
  "new_password": "newpassword456"
}
```

**参数说明**:
- `old_password`: 当前密码，用于验证身份
- `new_password`: 新密码，必须符合密码强度要求
  - 长度：6-128个字符
  - 格式：必须包含字母和数字
  - 不能与旧密码相同

**响应示例**:
```json
{
  "code": 200,
  "message": "Password changed successfully",
  "data": null
}
```

**错误响应**:
- `400 Bad Request`: 参数验证失败
  - 旧密码错误
  - 新密码格式不符合要求
  - 新密码与旧密码相同
  - 缺少必填参数
- `401 Unauthorized`: Token无效或已过期
- `403 Forbidden`: 游客用户无法修改密码
- `404 Not Found`: 用户不存在

**安全特性**:
- 必须提供正确的旧密码才能修改
- 新密码使用bcrypt加盐哈希存储
- 游客用户无法修改密码（需要先注册）
- 密码修改后原有token仍然有效

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 获取当前用户信息
async function getCurrentUser(token) {
  try {
    const response = await fetch('/api/v1/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('用户信息:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 2. 获取聊天会话列表
async function getUserChatSessions(token) {
  try {
    const response = await fetch('/api/v1/me/chats', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('会话列表:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 3. 修改密码
async function changePassword(oldPassword, newPassword, token) {
  try {
    const response = await fetch('/api/v1/me/password', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        old_password: oldPassword,
        new_password: newPassword
      })
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('密码修改成功');
      return true;
    } else {
      console.error('修改失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return false;
  }
}
```

### Vue.js 用户中心组件示例

```vue
<template>
  <div class="user-center">
    <!-- 用户信息卡片 -->
    <div class="user-info-card">
      <h2>个人信息</h2>
      <div v-if="userInfo">
        <p><strong>邮箱:</strong> {{ userInfo.email || '游客用户' }}</p>
        <p><strong>账户类型:</strong> {{ userInfo.is_guest ? '游客' : '注册用户' }}</p>
        <p><strong>账户状态:</strong> {{ userInfo.status }}</p>
        <p><strong>今日消息数:</strong> {{ userInfo.daily_message_count }}</p>
        <p><strong>剩余点数:</strong> {{ userInfo.message_credits }}</p>
        <p><strong>注册时间:</strong> {{ formatTime(userInfo.created_at) }}</p>
        <div v-if="userInfo.profile_summary">
          <strong>用户画像:</strong>
          <p>{{ userInfo.profile_summary }}</p>
        </div>
      </div>
    </div>

    <!-- 修改密码 -->
    <div class="password-section" v-if="userInfo && !userInfo.is_guest">
      <h3>修改密码</h3>
      <form @submit.prevent="changePassword">
        <div class="form-group">
          <label>当前密码:</label>
          <input 
            type="password" 
            v-model="passwordForm.oldPassword" 
            required 
            :disabled="changingPassword"
          />
        </div>
        <div class="form-group">
          <label>新密码:</label>
          <input 
            type="password" 
            v-model="passwordForm.newPassword" 
            required 
            minlength="6"
            pattern="^(?=.*[a-zA-Z])(?=.*\d).+$"
            title="密码必须包含字母和数字，至少6个字符"
            :disabled="changingPassword"
          />
        </div>
        <div class="form-group">
          <label>确认新密码:</label>
          <input 
            type="password" 
            v-model="passwordForm.confirmPassword" 
            required 
            :disabled="changingPassword"
          />
        </div>
        <button 
          type="submit" 
          :disabled="changingPassword || !isPasswordFormValid"
        >
          {{ changingPassword ? '修改中...' : '修改密码' }}
        </button>
      </form>
    </div>

    <!-- 聊天历史 -->
    <div class="chat-history-section">
      <h3>聊天历史</h3>
      <div v-if="chatSessions.length === 0" class="empty-state">
        暂无聊天记录
      </div>
      <div v-else class="session-list">
        <div 
          v-for="session in chatSessions" 
          :key="session.uuid"
          class="session-item"
          @click="openSession(session.uuid)"
        >
          <div class="session-info">
            <h4>{{ session.title }}</h4>
            <p>{{ session.ai_role_name }}</p>
            <small>{{ formatTime(session.updated_at) }}</small>
          </div>
          <div class="session-actions">
            <button @click.stop="openSession(session.uuid)">查看</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: null,
      chatSessions: [],
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      changingPassword: false,
      token: localStorage.getItem('token')
    }
  },

  computed: {
    isPasswordFormValid() {
      return this.passwordForm.oldPassword && 
             this.passwordForm.newPassword && 
             this.passwordForm.confirmPassword &&
             this.passwordForm.newPassword === this.passwordForm.confirmPassword &&
             this.passwordForm.newPassword.length >= 6 &&
             /^(?=.*[a-zA-Z])(?=.*\d)/.test(this.passwordForm.newPassword);
    }
  },

  async mounted() {
    await this.loadUserInfo();
    await this.loadChatSessions();
  },

  methods: {
    async loadUserInfo() {
      try {
        const response = await fetch('/api/v1/me', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.userInfo = result.data;
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
      }
    },

    async loadChatSessions() {
      try {
        const response = await fetch('/api/v1/me/chats', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.chatSessions = result.data;
        }
      } catch (error) {
        console.error('加载聊天历史失败:', error);
      }
    },

    async changePassword() {
      if (!this.isPasswordFormValid) return;

      this.changingPassword = true;
      try {
        const response = await fetch('/api/v1/me/password', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({
            old_password: this.passwordForm.oldPassword,
            new_password: this.passwordForm.newPassword
          })
        });

        const result = await response.json();
        if (result.code === 200) {
          alert('密码修改成功');
          this.passwordForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          };
        } else {
          alert('修改失败: ' + result.message);
        }
      } catch (error) {
        console.error('修改密码失败:', error);
        alert('修改失败，请重试');
      } finally {
        this.changingPassword = false;
      }
    },

    openSession(sessionUuid) {
      // 跳转到聊天页面
      this.$router.push(`/chat/${sessionUuid}`);
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    }
  }
}
</script>

<style scoped>
.user-center {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.user-info-card, .password-section, .chat-history-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
}

.session-item:hover {
  background-color: #f5f5f5;
}

.empty-state {
  text-align: center;
  color: #666;
  padding: 40px;
}

button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
```

---

## 数据库交互

### 相关表结构

**users表** - 存储用户基本信息
- 支持游客和注册用户
- 密码使用bcrypt加盐哈希
- 包含使用统计和画像信息

**chat_sessions表** - 存储聊天会话
- 关联用户和AI角色
- 按更新时间排序

**ai_roles表** - AI角色信息
- 提供角色名称用于会话列表显示

### 查询优化

- 用户信息查询使用主键索引
- 会话列表查询使用复合索引 `(user_id, updated_at)`
- 密码修改使用事务确保数据一致性

---

## 安全考虑

1. **密码安全**: 使用bcrypt加盐哈希，盐轮数12
2. **权限控制**: 所有接口都需要有效JWT认证
3. **游客限制**: 游客用户无法修改密码
4. **参数验证**: 严格的输入验证和格式检查
5. **错误处理**: 不泄露敏感信息的错误响应

---

## 注意事项

1. **游客用户**: 游客用户的email字段为null，无法修改密码
2. **密码强度**: 新密码必须包含字母和数字，长度6-128字符
3. **会话列表**: 按最后更新时间倒序，便于用户查看最近对话
4. **用户画像**: profile_summary字段用于存储AI分析的用户兴趣
5. **使用统计**: daily_message_count用于限制每日免费使用次数

---

## 测试覆盖

模块四包含完整的测试覆盖：
- ✅ 用户信息获取测试（注册用户和游客）
- ✅ 聊天会话列表获取测试
- ✅ 密码修改功能测试
- ✅ 密码验证测试（旧密码错误、新密码格式等）
- ✅ 权限验证测试（游客无法修改密码）
- ✅ 参数验证测试（缺少参数、格式错误等）
- ✅ 认证测试（无效token、缺少token等）

所有15个测试用例均已通过验证。
