import axios from 'axios'
import { useAuthStore } from '@/stores/counter'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理成功响应
    if (response.data && response.data.code === 200) {
      return response.data.data
    } else if (response.data && response.data.code === 201) {
      return response.data.data
    }
    return response.data
  },
  (error) => {
    const authStore = useAuthStore()
    
    // 处理网络错误
    if (!error.response) {
      throw new Error('网络连接失败，请检查网络设置')
    }

    const { status, data } = error.response

    // 处理认证错误
    if (status === 401) {
      authStore.logout()
      throw new Error('登录已过期，请重新登录')
    }

    // 处理其他HTTP错误
    const errorMessage = data?.message || getDefaultErrorMessage(status)
    throw new Error(errorMessage)
  }
)

// 获取默认错误消息
function getDefaultErrorMessage(status) {
  const errorMessages = {
    400: '请求参数有误',
    403: '没有权限访问',
    404: '请求的资源不存在',
    409: '资源冲突',
    500: '服务器内部错误，请稍后重试'
  }
  return errorMessages[status] || '请求失败，请稍后重试'
}

// 认证相关API
export const authAPI = {
  // 创建游客账户
  async createGuest() {
    return await apiClient.post('/auth/guest')
  },

  // 用户注册
  async register(email, password) {
    return await apiClient.post('/auth/register', {
      email,
      password
    })
  },

  // 用户登录
  async login(email, password) {
    return await apiClient.post('/auth/login', {
      email,
      password
    })
  },

  // 获取当前用户信息
  async getCurrentUser() {
    return await apiClient.get('/auth/me')
  }
}

// AI角色相关API
export const aiRoleAPI = {
  // 获取AI角色列表
  async getAIRoles() {
    return await apiClient.get('/ai-roles')
  },

  // 获取AI角色详情
  async getAIRoleDetail(id) {
    return await apiClient.get(`/ai-roles/${id}`)
  }
}

// 聊天相关API
export const chatAPI = {
  // 创建新的聊天会话
  async createChatSession(aiRoleId) {
    return await apiClient.post('/chat/sessions', {
      ai_role_id: aiRoleId
    })
  },

  // 获取用户的聊天会话列表
  async getChatSessions() {
    return await apiClient.get('/chat/sessions')
  },

  // 发送消息（非流式）
  async sendMessage(sessionUuid, content) {
    return await apiClient.post(`/chat/sessions/${sessionUuid}/messages`, {
      content
    })
  },

  // 发送消息（流式）
  async sendMessageStream(sessionUuid, content, onChunk, onComplete, onError) {
    try {
      const response = await fetch(`${apiClient.defaults.baseURL}/chat/sessions/${sessionUuid}/messages/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ content })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n\n')
        buffer = lines.pop() // 保留不完整的最后一行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              return
            }

            try {
              const event = JSON.parse(data)

              switch (event.type) {
                case 'connected':
                  console.log('流式连接已建立')
                  break
                case 'chunk':
                  onChunk && onChunk(event)
                  break
                case 'complete':
                  onComplete && onComplete(event.message)
                  return
                case 'error':
                  onError && onError(new Error(event.message))
                  return
              }
            } catch (parseError) {
              console.warn('解析SSE事件失败:', parseError)
            }
          }
        }
      }
    } catch (error) {
      onError && onError(error)
      throw error
    }
  },

  // 获取会话历史消息
  async getChatMessages(sessionUuid) {
    return await apiClient.get(`/chat/sessions/${sessionUuid}/messages`)
  }
}

// 用户中心相关API
export const userAPI = {
  // 获取当前用户信息
  async getCurrentUser() {
    return await apiClient.get('/me')
  },

  // 获取用户的聊天会话列表
  async getUserChatSessions() {
    return await apiClient.get('/me/chats')
  },

  // 修改密码
  async changePassword(oldPassword, newPassword) {
    return await apiClient.put('/me/password', {
      old_password: oldPassword,
      new_password: newPassword
    })
  },

  // 获取用户订阅信息
  async getUserSubscriptions() {
    return await apiClient.get('/me/subscriptions')
  },

  // 获取用户订单历史
  async getUserOrders() {
    try {
      const response = await apiClient.get('/me/orders')
      return response
    } catch (error) {
      console.error('获取订单历史失败:', error)
      throw error
    }
  }
}

// 支付相关API
export const paymentAPI = {
  // 获取所有可用套餐
  async getPlans() {
    return await apiClient.get('/plans')
  },

  // 创建订单
  async createOrder(planId) {
    return await apiClient.post('/orders', {
      plan_id: planId
    })
  },

  // 模拟支付通知（仅用于测试）
  async simulatePayment(orderUuid, status = 'success') {
    const response = await fetch(`${apiClient.defaults.baseURL}/payments/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        order_uuid: orderUuid,
        status: status,
        transaction_id: 'mock_txn_' + Date.now(),
        gateway: 'mock_payment'
      })
    })

    return await response.text()
  }
}

// 导出默认的API客户端
export default apiClient
