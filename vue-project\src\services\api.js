import axios from 'axios'
import { useAuthStore } from '@/stores/counter'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理成功响应
    if (response.data && (response.data.code === 200 || response.data.code === 201)) {
      return response.data.data
    }
    return response.data
  },
  (error) => {
    const authStore = useAuthStore()

    // 处理网络错误
    if (!error.response) {
      throw new Error('网络连接失败，请检查网络设置')
    }

    const { status, data } = error.response

    // 处理认证错误
    if (status === 401) {
      authStore.logout()
      throw new Error('登录已过期，请重新登录')
    }

    // 处理其他HTTP错误
    const errorMessage = data?.message || getDefaultErrorMessage(status)
    throw new Error(errorMessage)
  }
)

// 获取默认错误消息
function getDefaultErrorMessage(status) {
  const errorMessages = {
    400: '请求参数有误',
    403: '没有权限访问',
    404: '请求的资源不存在',
    409: '资源冲突',
    500: '服务器内部错误，请稍后重试'
  }
  return errorMessages[status] || '请求失败，请稍后重试'
}

// 认证相关API
export const authAPI = {
  // 创建游客账户
  async createGuest() {
    return await apiClient.post('/auth/guest')
  },

  // 用户注册
  async register(email, password) {
    return await apiClient.post('/auth/register', {
      email,
      password
    })
  },

  // 用户登录
  async login(email, password) {
    return await apiClient.post('/auth/login', {
      email,
      password
    })
  },

  // 获取当前用户信息
  async getCurrentUser() {
    return await apiClient.get('/auth/me')
  }
}

// AI角色相关API
export const aiRoleAPI = {
  // 获取AI角色列表
  async getAIRoles() {
    return await apiClient.get('/ai-roles')
  },

  // 获取AI角色详情
  async getAIRoleDetail(id) {
    return await apiClient.get(`/ai-roles/${id}`)
  }
}

// 聊天相关API
export const chatAPI = {
  // 创建新的聊天会话
  async createChatSession(aiRoleId) {
    return await apiClient.post('/chat/sessions', {
      ai_role_id: aiRoleId
    })
  },

  // 获取用户的聊天会话列表
  async getChatSessions() {
    return await apiClient.get('/chat/sessions')
  },

  // 获取指定会话的详情
  async getChatSessionDetail(sessionUuid) {
    return await apiClient.get(`/chat/sessions/${sessionUuid}`)
  },

  // 发送消息
  async sendMessage(sessionUuid, content) {
    return await apiClient.post(`/chat/sessions/${sessionUuid}/messages`, {
      content
    })
  },

  // 获取会话历史消息
  async getChatMessages(sessionUuid) {
    return await apiClient.get(`/chat/sessions/${sessionUuid}/messages`)
  },

  // 删除聊天会话
  async deleteChatSession(sessionUuid) {
    return await apiClient.delete(`/chat/sessions/${sessionUuid}`)
  }
}

// 用户中心相关API
export const userAPI = {
  // 获取用户的聊天会话列表
  async getUserChatSessions() {
    return await apiClient.get('/me/chats')
  },

  // 获取用户订阅信息
  async getUserSubscriptions() {
    return await apiClient.get('/me/subscriptions')
  }
}

// 支付相关API
export const paymentAPI = {
  // 获取套餐列表
  async getPlans() {
    return await apiClient.get('/plans')
  },

  // 创建订单
  async createOrder(planId) {
    return await apiClient.post('/orders', {
      plan_id: planId
    })
  }
}

// 导出默认的API客户端
export default apiClient
