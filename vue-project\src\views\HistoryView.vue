<template>
  <div class="history-view">
    <div class="container">
      <div class="history-placeholder">
        <h1 class="text-xl font-semibold text-primary">聊天历史</h1>
        <p class="text-secondary">聊天历史功能将在后续模块中实现</p>
        <router-link to="/" class="btn btn-primary">
          返回首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.history-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-placeholder {
  text-align: center;
  padding: var(--space-xl);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
}

.history-placeholder h1 {
  margin-bottom: var(--space-md);
}

.history-placeholder p {
  margin-bottom: var(--space-lg);
}
</style>
