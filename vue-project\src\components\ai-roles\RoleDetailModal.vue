<template>
  <Teleport to="body">
    <div v-if="isVisible" class="role-detail-overlay" @click="handleOverlayClick">
      <div class="role-detail-modal" @click.stop>
        <button class="modal-close" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <div class="modal-content" v-if="roleDetails">
          <!-- 角色头部信息 -->
          <div class="role-header">
            <div class="role-avatar-large">
              <img 
                v-if="roleDetails.avatar_url" 
                :src="roleDetails.avatar_url" 
                :alt="roleDetails.name"
                class="avatar-image"
                @error="handleImageError"
              />
              <div v-else class="avatar-placeholder">
                <span class="avatar-initial">{{ roleDetails.name.charAt(0) }}</span>
              </div>
              <div class="avatar-glow"></div>
            </div>
            
            <div class="role-info">
              <h2 class="role-name">{{ roleDetails.name }}</h2>
              <div class="role-badge">AI伙伴</div>
              <p class="role-description">{{ roleDetails.description }}</p>
            </div>
          </div>

          <!-- 角色详细介绍 -->
          <div class="role-details" v-if="roleDetails.details">
            <h3 class="section-title">角色背景</h3>
            <div class="details-content">
              <p>{{ roleDetails.details }}</p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="modal-actions">
            <button 
              class="btn btn-primary btn-large"
              @click="handleStartChat"
              :disabled="isLoading"
            >
              <span v-if="isLoading" class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              <span v-else>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 12L12 8L16 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 8V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                开始与{{ roleDetails.name }}聊天
              </span>
            </button>
          </div>

          <!-- 错误提示 -->
          <div v-if="error" class="error-alert">
            {{ error }}
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else-if="isLoadingDetails" class="loading-state">
          <div class="loading-spinner">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <p class="loading-text">正在加载角色详情...</p>
        </div>

        <!-- 加载失败 -->
        <div v-else class="error-state">
          <div class="error-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h3 class="error-title">加载失败</h3>
          <p class="error-message">{{ error || '无法加载角色详情' }}</p>
          <button class="btn btn-secondary" @click="loadRoleDetails">
            重试
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { aiRoleAPI } from '@/services/api'

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  role: {
    type: Object,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['close', 'start-chat'])

// 响应式数据
const isVisible = ref(props.visible)
const roleDetails = ref(null)
const isLoadingDetails = ref(false)
const isLoading = ref(false)
const error = ref('')

// 计算属性
const currentRoleId = computed(() => props.role?.id)

// 监听props变化
watch(() => props.visible, (newValue) => {
  isVisible.value = newValue
  if (newValue && props.role) {
    loadRoleDetails()
    // 防止背景滚动
    document.body.style.overflow = 'hidden'
  } else {
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
})

watch(() => props.role, (newRole) => {
  if (newRole && isVisible.value) {
    loadRoleDetails()
  }
})

// 加载角色详情
async function loadRoleDetails() {
  if (!props.role?.id) {
    error.value = '无效的角色ID'
    return
  }

  isLoadingDetails.value = true
  error.value = ''
  roleDetails.value = null

  try {
    const details = await aiRoleAPI.getAIRoleDetail(props.role.id)
    if (details && typeof details === 'object') {
      roleDetails.value = details
    } else {
      throw new Error('角色详情数据格式错误')
    }
  } catch (err) {
    error.value = err.message || '加载角色详情失败'
    roleDetails.value = null
  } finally {
    isLoadingDetails.value = false
  }
}

// 关闭模态框
function closeModal() {
  isVisible.value = false
  document.body.style.overflow = ''
  emit('close')
}

// 处理点击遮罩层
function handleOverlayClick() {
  closeModal()
}

// 处理开始聊天
function handleStartChat() {
  if (roleDetails.value) {
    emit('start-chat', roleDetails.value)
  }
}

// 处理图片加载错误
function handleImageError(event) {
  if (event.target) {
    event.target.style.display = 'none'
    const placeholder = event.target.parentElement?.querySelector('.avatar-placeholder')
    if (placeholder) {
      placeholder.style.display = 'flex'
    }
  }
}

// 组件卸载时恢复滚动
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style scoped>
.role-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--space-lg);
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-detail-modal {
  position: relative;
  background: var(--color-background-dark);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-glass);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  max-width: 600px;
  overflow-y: auto;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  width: 32px;
  height: 32px;
  background: var(--color-surface-primary);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 1;
}

.modal-close:hover {
  background: var(--color-surface-secondary);
  color: var(--color-text-primary);
  transform: scale(1.05);
}

.modal-content {
  padding: var(--space-xl);
  padding-top: calc(var(--space-xl) + 32px + var(--space-lg));
}

.role-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--space-xl);
  gap: var(--space-lg);
}

.role-avatar-large {
  position: relative;
  width: 120px;
  height: 120px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-xl);
  object-fit: cover;
  border: 3px solid var(--color-border-glass);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--color-border-glass);
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

.avatar-glow {
  position: absolute;
  inset: -6px;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-xl);
  opacity: 0.3;
  z-index: -1;
  filter: blur(12px);
  animation: pulse 2s ease-in-out infinite;
}

.role-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
}

.role-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  letter-spacing: -0.02em;
}

.role-badge {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 6px 16px;
  border-radius: var(--border-radius-md);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.role-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  max-width: 400px;
}

.role-details {
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  text-align: center;
}

.details-content {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
}

.details-content p {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

.modal-actions {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.btn-large {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

/* 加载和错误状态样式 */
.loading-state, .error-state {
  text-align: center;
  padding: var(--space-3xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

.error-alert {
  margin-top: var(--space-lg);
  padding: var(--space-md);
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--color-error);
  border-radius: var(--border-radius-sm);
  color: var(--color-error);
  font-size: var(--font-size-sm);
  text-align: center;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .role-detail-overlay {
    padding: var(--space-md);
  }
  
  .modal-content {
    padding: var(--space-lg);
    padding-top: calc(var(--space-lg) + 32px + var(--space-md));
  }
  
  .modal-close {
    top: var(--space-md);
    right: var(--space-md);
  }

  .role-header {
    gap: var(--space-md);
  }

  .role-avatar-large {
    width: 100px;
    height: 100px;
  }

  .role-name {
    font-size: var(--font-size-lg);
  }
}
</style>
