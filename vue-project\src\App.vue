<script setup>
import { RouterView, useRoute } from 'vue-router'
import { onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/counter'
import AppHeader from '@/components/layout/AppHeader.vue'

// 路由和状态管理
const route = useRoute()
const authStore = useAuthStore()

// 计算是否显示头部导航
const showHeader = computed(() => {
  // 聊天页面不显示全局头部导航
  return route.name !== 'chat'
})

// 初始化应用
onMounted(() => {
  // 初始化认证状态
  authStore.initializeAuth()
})
</script>

<template>
  <div id="app" :class="{ 'chat-mode': !showHeader }">
    <AppHeader v-if="showHeader" />
    <main class="main-content" :class="{ 'full-height': !showHeader }">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#app.chat-mode {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content.full-height {
  height: 100vh;
  overflow: hidden;
}
</style>
