<script setup>
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/counter'
import AppHeader from '@/components/layout/AppHeader.vue'

// 状态管理
const authStore = useAuthStore()

// 初始化应用
onMounted(() => {
  // 初始化认证状态
  authStore.initializeAuth()
})
</script>

<template>
  <div id="app">
    <AppHeader />
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
