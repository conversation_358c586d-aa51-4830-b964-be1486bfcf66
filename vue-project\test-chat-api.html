<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input, textarea {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        textarea {
            width: 100%;
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>聊天API测试</h1>
    
    <div class="test-section">
        <h2>1. 创建聊天会话</h2>
        <input type="number" id="role-id" placeholder="AI角色ID" value="1" min="1">
        <button onclick="createSession()">创建会话</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 获取会话列表</h2>
        <button onclick="getSessions()">获取会话列表</button>
        <div id="sessions-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 发送消息</h2>
        <input type="text" id="session-uuid" placeholder="会话UUID">
        <textarea id="message-content" placeholder="消息内容">你好</textarea>
        <button onclick="sendMessage()">发送消息</button>
        <div id="message-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 获取消息历史</h2>
        <input type="text" id="history-session-uuid" placeholder="会话UUID">
        <button onclick="getMessages()">获取消息</button>
        <div id="history-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let authToken = localStorage.getItem('auth_token');

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken ? `Bearer ${authToken}` : '',
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                return { response, data };
            } catch (error) {
                throw new Error(`网络错误: ${error.message}`);
            }
        }

        async function createSession() {
            const roleId = document.getElementById('role-id').value;
            
            if (!roleId) {
                displayResult('create-result', { error: '请输入角色ID' }, true);
                return;
            }

            try {
                const { response, data } = await makeRequest(`${API_BASE_URL}/chat/sessions`, {
                    method: 'POST',
                    body: JSON.stringify({ ai_role_id: parseInt(roleId) })
                });

                if (response.ok) {
                    displayResult('create-result', data);
                    // 自动填充会话UUID到其他输入框
                    if (data.data && data.data.uuid) {
                        document.getElementById('session-uuid').value = data.data.uuid;
                        document.getElementById('history-session-uuid').value = data.data.uuid;
                    }
                } else {
                    displayResult('create-result', data, true);
                }
            } catch (error) {
                displayResult('create-result', { error: error.message }, true);
            }
        }

        async function getSessions() {
            try {
                const { response, data } = await makeRequest(`${API_BASE_URL}/chat/sessions`);

                if (response.ok) {
                    displayResult('sessions-result', data);
                } else {
                    displayResult('sessions-result', data, true);
                }
            } catch (error) {
                displayResult('sessions-result', { error: error.message }, true);
            }
        }

        async function sendMessage() {
            const sessionUuid = document.getElementById('session-uuid').value;
            const content = document.getElementById('message-content').value;
            
            if (!sessionUuid || !content) {
                displayResult('message-result', { error: '请输入会话UUID和消息内容' }, true);
                return;
            }

            try {
                const { response, data } = await makeRequest(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages`, {
                    method: 'POST',
                    body: JSON.stringify({ content })
                });

                if (response.ok) {
                    displayResult('message-result', data);
                } else {
                    displayResult('message-result', data, true);
                }
            } catch (error) {
                displayResult('message-result', { error: error.message }, true);
            }
        }

        async function getMessages() {
            const sessionUuid = document.getElementById('history-session-uuid').value;
            
            if (!sessionUuid) {
                displayResult('history-result', { error: '请输入会话UUID' }, true);
                return;
            }

            try {
                const { response, data } = await makeRequest(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages`);

                if (response.ok) {
                    displayResult('history-result', data);
                } else {
                    displayResult('history-result', data, true);
                }
            } catch (error) {
                displayResult('history-result', { error: error.message }, true);
            }
        }

        // 页面加载时检查认证状态
        window.onload = function() {
            if (!authToken) {
                alert('请先登录获取认证token');
                // 可以跳转到登录页面
                // window.location.href = '/';
            } else {
                console.log('使用token:', authToken);
                getSessions(); // 自动加载会话列表
            }
        };
    </script>
</body>
</html>
