# AI伙伴聊天应用 - 模块三API文档

## 概述

本文档描述了AI伙伴聊天应用**模块三：核心对话功能**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（所有接口都需要认证）

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "聊天会话不存在或无权访问",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 201 | 201 | 资源创建成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |
| 500 | 50002 | AI回复生成失败 |

---

## 接口列表

### 1. 创建聊天会话

**接口描述**: 创建一个新的聊天会话，指定AI角色

- **URL**: `POST /chat/sessions`
- **认证**: 需要JWT Token

**请求参数**:
```json
{
  "ai_role_id": 1
}
```

**参数说明**:
- `ai_role_id`: AI角色ID，必须是正整数，且角色必须存在并启用

**响应示例**:
```json
{
  "code": 201,
  "message": "Chat session created successfully",
  "data": {
    "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "ai_role_id": 1,
    "title": "新的对话",
    "created_at": "2025-07-22T10:00:00.000Z"
  }
}
```

**错误响应**:
- `404 Not Found`: AI角色不存在或已禁用
- `400 Bad Request`: 参数验证失败
- `401 Unauthorized`: 未认证

### 2. 获取用户聊天会话列表

**接口描述**: 获取当前用户的所有聊天会话，按最后更新时间倒序排列

- **URL**: `GET /chat/sessions`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Sessions retrieved successfully",
  "data": [
    {
      "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "title": "新的对话",
      "ai_role_id": 1,
      "ai_role_name": "博学的历史学家",
      "updated_at": "2025-07-22T10:30:00.000Z"
    },
    {
      "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
      "title": "新的对话",
      "ai_role_id": 2,
      "ai_role_name": "温柔的心理顾问",
      "updated_at": "2025-07-22T09:15:00.000Z"
    }
  ]
}
```

**字段说明**:
- `uuid`: 会话的唯一标识符
- `title`: 会话标题
- `ai_role_id`: 关联的AI角色ID
- `ai_role_name`: AI角色名称
- `updated_at`: 最后更新时间

### 3. 发送消息（非流式）

**接口描述**: 向指定会话发送消息并获取AI回复（一次性返回完整回复）

- **URL**: `POST /chat/sessions/{session_uuid}/messages`
- **认证**: 需要JWT Token

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| session_uuid | string | 是 | 会话UUID，必须是有效的UUID格式 |

**请求参数**:
```json
{
  "content": "你好，我想了解一下古代中国的历史"
}
```

**参数说明**:
- `content`: 消息内容，1-2000个字符，不能为空

**响应示例**:
```json
{
  "code": 200,
  "message": "Message sent successfully",
  "data": {
    "role": "assistant",
    "content": "您好！我很高兴为您介绍古代中国的历史。中国有着五千年的悠久历史，从传说中的三皇五帝时代开始，经历了夏、商、周等朝代的更迭...",
    "emotion": null,
    "created_at": "2025-07-22T10:31:25.123Z"
  }
}
```

**字段说明**:
- `role`: 消息发送方，固定为"assistant"
- `content`: AI回复的内容
- `emotion`: 情绪分析结果（当前版本为null）
- `created_at`: 消息创建时间（毫秒精度）

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: 参数验证失败
- `500 Internal Server Error`: AI回复生成失败

### 4. 发送消息（流式）

**接口描述**: 向指定会话发送消息并获取AI流式回复（实时返回生成过程）

- **URL**: `POST /chat/sessions/{session_uuid}/messages/stream`
- **认证**: 需要JWT Token
- **响应类型**: Server-Sent Events (SSE)

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| session_uuid | string | 是 | 会话UUID，必须是有效的UUID格式 |

**请求参数**:
```json
{
  "content": "你好，我想了解一下古代中国的历史"
}
```

**响应格式**: Server-Sent Events
```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"connected"}

data: {"type":"chunk","content":"您好","fullContent":"您好"}

data: {"type":"chunk","content":"！我很","fullContent":"您好！我很"}

data: {"type":"chunk","content":"高兴为您","fullContent":"您好！我很高兴为您"}

data: {"type":"complete","message":{"role":"assistant","content":"您好！我很高兴为您介绍古代中国的历史...","emotion":null,"created_at":"2025-07-22T10:31:25.123Z"}}

data: [DONE]
```

**事件类型说明**:
- `connected`: 连接建立确认
- `chunk`: 流式数据块
  - `content`: 当前数据块内容
  - `fullContent`: 累积的完整内容
- `complete`: 生成完成
  - `message`: 完整的AI消息对象
- `error`: 错误信息
  - `code`: 错误码
  - `message`: 错误描述

**JavaScript接收示例**:
```javascript
async function sendMessageStream(sessionUuid, content, token) {
  const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages/stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ content })
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = '';

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split('\n\n');
    buffer = lines.pop(); // 保留不完整的最后一行

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6);

        if (data === '[DONE]') {
          console.log('流式响应完成');
          return;
        }

        try {
          const event = JSON.parse(data);

          switch (event.type) {
            case 'connected':
              console.log('连接已建立');
              break;
            case 'chunk':
              console.log('收到数据块:', event.content);
              // 实时更新UI显示
              updateChatUI(event.fullContent);
              break;
            case 'complete':
              console.log('AI回复完成:', event.message);
              break;
            case 'error':
              console.error('错误:', event.message);
              break;
          }
        } catch (e) {
          console.warn('解析事件失败:', e);
        }
      }
    }
  }
}
```

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问（通过SSE error事件返回）
- `400 Bad Request`: 参数验证失败（HTTP状态码）
- `500 Internal Server Error`: AI回复生成失败（通过SSE error事件返回）

### 5. 获取会话历史消息

**接口描述**: 获取指定会话的所有历史消息，按时间升序排列

- **URL**: `GET /chat/sessions/{session_uuid}/messages`
- **认证**: 需要JWT Token

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| session_uuid | string | 是 | 会话UUID，必须是有效的UUID格式 |

**响应示例**:
```json
{
  "code": 200,
  "message": "Messages retrieved successfully",
  "data": [
    {
      "role": "user",
      "content": "你好，我想了解一下古代中国的历史",
      "emotion": null,
      "created_at": "2025-07-22T10:31:20.456Z"
    },
    {
      "role": "assistant",
      "content": "您好！我很高兴为您介绍古代中国的历史。中国有着五千年的悠久历史，从传说中的三皇五帝时代开始，经历了夏、商、周等朝代的更迭...",
      "emotion": null,
      "created_at": "2025-07-22T10:31:25.123Z"
    }
  ]
}
```

**字段说明**:
- `role`: 消息发送方，"user"表示用户，"assistant"表示AI
- `content`: 消息内容
- `emotion`: 情绪分析结果（当前版本为null）
- `created_at`: 消息创建时间（毫秒精度）

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: 参数验证失败

---

## 参数验证规则

### 会话UUID验证
- **格式**: 标准UUID格式（8-4-4-4-12）
- **示例**: `a1b2c3d4-e5f6-7890-1234-567890abcdef`

### AI角色ID验证
- **类型**: 正整数
- **范围**: 必须大于0
- **存在性**: 必须是已启用的AI角色

### 消息内容验证
- **长度**: 1-2000个字符
- **格式**: 去除首尾空格后不能为空
- **编码**: UTF-8

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 创建聊天会话
async function createChatSession(aiRoleId, token) {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        ai_role_id: aiRoleId
      })
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('会话创建成功:', result.data);
      return result.data.uuid;
    } else {
      console.error('创建失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 2. 发送消息
async function sendMessage(sessionUuid, content, token) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        content: content
      })
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('AI回复:', result.data.content);
      return result.data;
    } else {
      console.error('发送失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 3. 获取历史消息
async function getMessages(sessionUuid, token) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('历史消息:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 4. 获取会话列表
async function getSessions(token) {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('会话列表:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}
```

### Vue.js 聊天组件示例

```vue
<template>
  <div class="chat-container">
    <!-- 会话列表 -->
    <div class="session-list">
      <button @click="createNewSession">新建对话</button>
      <div 
        v-for="session in sessions" 
        :key="session.uuid"
        @click="selectSession(session.uuid)"
        :class="{ active: currentSessionUuid === session.uuid }"
      >
        <h4>{{ session.title }}</h4>
        <p>{{ session.ai_role_name }}</p>
        <small>{{ formatTime(session.updated_at) }}</small>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div class="chat-area" v-if="currentSessionUuid">
      <div class="messages" ref="messagesContainer">
        <div
          v-for="message in messages"
          :key="message.created_at"
          :class="['message', message.role, { streaming: message.streaming, error: message.error }]"
        >
          <div class="content">
            {{ message.content }}
            <span v-if="message.streaming" class="typing-indicator">▋</span>
          </div>
          <div class="time">{{ formatTime(message.created_at) }}</div>
        </div>
      </div>

      <div class="input-area">
        <textarea 
          v-model="inputMessage" 
          @keydown.enter.prevent="sendMessage"
          placeholder="输入消息..."
          :disabled="sending"
        ></textarea>
        <button @click="sendMessage" :disabled="sending || !inputMessage.trim()">
          {{ sending ? '发送中...' : '发送' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sessions: [],
      currentSessionUuid: null,
      messages: [],
      inputMessage: '',
      sending: false,
      token: localStorage.getItem('token')
    }
  },

  async mounted() {
    await this.loadSessions();
  },

  methods: {
    async loadSessions() {
      try {
        const response = await fetch('/api/v1/chat/sessions', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.sessions = result.data;
        }
      } catch (error) {
        console.error('加载会话列表失败:', error);
      }
    },

    async createNewSession() {
      // 假设已选择AI角色ID
      const aiRoleId = 1;
      try {
        const response = await fetch('/api/v1/chat/sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({ ai_role_id: aiRoleId })
        });
        const result = await response.json();
        if (result.code === 201) {
          await this.loadSessions();
          this.selectSession(result.data.uuid);
        }
      } catch (error) {
        console.error('创建会话失败:', error);
      }
    },

    async selectSession(sessionUuid) {
      this.currentSessionUuid = sessionUuid;
      await this.loadMessages();
    },

    async loadMessages() {
      try {
        const response = await fetch(`/api/v1/chat/sessions/${this.currentSessionUuid}/messages`, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.messages = result.data;
        }
      } catch (error) {
        console.error('加载消息失败:', error);
      }
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) return;

      const content = this.inputMessage.trim();
      this.inputMessage = '';
      this.sending = true;

      // 立即显示用户消息
      this.messages.push({
        role: 'user',
        content: content,
        created_at: new Date().toISOString()
      });

      // 添加AI消息占位符
      const aiMessageIndex = this.messages.length;
      this.messages.push({
        role: 'assistant',
        content: '',
        created_at: new Date().toISOString(),
        streaming: true
      });

      try {
        await this.sendMessageStream(content, aiMessageIndex);
      } catch (error) {
        console.error('发送消息失败:', error);
        // 更新AI消息为错误状态
        this.messages[aiMessageIndex] = {
          role: 'assistant',
          content: '抱歉，我现在无法回复您的消息，请稍后再试。',
          created_at: new Date().toISOString(),
          error: true
        };
      } finally {
        this.sending = false;
      }
    },

    async sendMessageStream(content, aiMessageIndex) {
      const response = await fetch(`/api/v1/chat/sessions/${this.currentSessionUuid}/messages/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({ content })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n\n');
        buffer = lines.pop();

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              // 流式响应完成
              this.messages[aiMessageIndex].streaming = false;
              await this.loadSessions(); // 更新会话列表
              return;
            }

            try {
              const event = JSON.parse(data);

              switch (event.type) {
                case 'connected':
                  console.log('流式连接已建立');
                  break;

                case 'chunk':
                  // 实时更新AI回复内容
                  this.messages[aiMessageIndex].content = event.fullContent;
                  // 自动滚动到底部
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });
                  break;

                case 'complete':
                  // 完成时更新为最终消息
                  this.messages[aiMessageIndex] = {
                    ...event.message,
                    streaming: false
                  };
                  break;

                case 'error':
                  throw new Error(event.message);
              }
            } catch (parseError) {
              console.warn('解析SSE事件失败:', parseError);
            }
          }
        }
      }
    },

    scrollToBottom() {
      const messagesContainer = this.$refs.messagesContainer;
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    }
  }
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
}

.session-list {
  width: 300px;
  border-right: 1px solid #eee;
  overflow-y: auto;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.message {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
}

.message.user {
  background-color: #007bff;
  color: white;
  margin-left: 20%;
}

.message.assistant {
  background-color: #f8f9fa;
  margin-right: 20%;
}

.message.streaming {
  background-color: #e3f2fd;
}

.message.error {
  background-color: #ffebee;
  color: #c62828;
}

.typing-indicator {
  animation: blink 1s infinite;
  color: #007bff;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.input-area {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 10px;
}

.input-area textarea {
  flex: 1;
  min-height: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  resize: vertical;
}

.input-area button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.input-area button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
```

---

## 数据库结构

### chat_sessions表结构

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT UNSIGNED | PRIMARY KEY, AUTO_INCREMENT | 内部主键 |
| uuid | CHAR(36) | UNIQUE, NOT NULL | 对外公开ID |
| user_id | BIGINT UNSIGNED | NOT NULL, FK | 关联用户ID |
| ai_role_id | INT UNSIGNED | NOT NULL, FK | 关联AI角色ID |
| title | VARCHAR(255) | NULL | 会话标题 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### chat_messages表结构

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT UNSIGNED | PRIMARY KEY, AUTO_INCREMENT | 消息主键 |
| session_id | BIGINT UNSIGNED | NOT NULL, FK | 关联会话ID |
| role | ENUM('user', 'assistant') | NOT NULL | 消息发送方 |
| content | TEXT | NOT NULL | 消息内容 |
| emotion | VARCHAR(50) | NULL | 情绪分析结果 |
| created_at | TIMESTAMP(3) | NOT NULL, DEFAULT CURRENT_TIMESTAMP(3) | 创建时间（毫秒精度） |

---

## LLM集成说明

### 使用的LLM服务
- **提供商**: SophNet
- **模型**: DeepSeek-V3-Fast
- **API地址**: https://www.sophnet.com/api/open-apis/v1

### 配置参数
- **max_tokens**: 2000
- **temperature**: 0.7
- **stream**: false（同步调用）

### 上下文管理
- 每次调用包含最近10条消息作为上下文
- 自动包含AI角色的system_prompt
- 按时间顺序组织消息历史

### 错误处理
- API调用超时：30秒
- 自动重试机制：无（单次调用）
- 失败时返回友好错误消息

---

## 注意事项

1. **认证要求**: 所有接口都需要有效的JWT Token
2. **权限控制**: 用户只能访问自己创建的会话
3. **消息长度**: 单条消息最大2000字符
4. **LLM调用**: 可能耗时较长（通常2-10秒）
5. **错误处理**: 前端需要处理LLM调用失败的情况
6. **实时性**: 当前版本不支持流式响应
7. **上下文限制**: 只保留最近10条消息作为上下文

---

## 测试覆盖

模块三包含完整的测试覆盖：

### 非流式接口测试
- ✅ 会话创建测试（注册用户和游客）
- ✅ 会话列表获取测试
- ✅ 消息发送测试（包含真实LLM调用）
- ✅ 历史消息获取测试
- ✅ 参数验证测试（UUID格式、消息长度等）
- ✅ 权限验证测试（认证、会话所有权等）
- ✅ 错误处理测试（不存在的会话、角色等）

### 流式接口测试
- ✅ SSE连接建立测试
- ✅ 流式数据接收测试（包含真实LLM流式调用）
- ✅ 流式事件格式验证（connected、chunk、complete、error）
- ✅ 流式参数验证测试
- ✅ 流式权限验证测试
- ✅ 流式错误处理测试

**总计**: 20个测试用例全部通过，包括真实的LLM API调用（非流式和流式）。

### 性能表现
- 非流式调用：约4-5秒完成
- 流式调用：约7-8秒完成，但用户可实时看到生成过程
- 流式响应显著提升用户体验
