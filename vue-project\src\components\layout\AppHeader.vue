<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- <PERSON><PERSON>和品牌 -->
        <div class="brand">
          <router-link to="/" class="brand-link">
            <div class="brand-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                <path d="M12 16L13.09 22.26L22 23L13.09 23.74L12 30L10.91 23.74L2 23L10.91 22.26L12 16Z" fill="currentColor" opacity="0.6"/>
              </svg>
            </div>
            <span class="brand-text">AI伙伴</span>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <nav class="main-nav" v-if="!isMobile">
          <router-link 
            to="/" 
            class="nav-link"
            :class="{ active: $route.name === 'home' }"
          >
            首页
          </router-link>
          <router-link 
            to="/ai-roles" 
            class="nav-link"
            :class="{ active: $route.name === 'ai-roles' }"
          >
            AI角色
          </router-link>
          <router-link 
            v-if="authStore.isAuthenticated"
            to="/chat" 
            class="nav-link"
            :class="{ active: $route.name === 'chat' }"
          >
            聊天
          </router-link>
        </nav>

        <!-- 用户菜单 -->
        <div class="header-actions">
          <UserMenu />
          
          <!-- 移动端菜单按钮 -->
          <button 
            v-if="isMobile"
            class="mobile-menu-btn"
            @click="toggleMobileMenu"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端导航菜单 -->
      <div v-if="isMobile && showMobileMenu" class="mobile-nav">
        <router-link 
          to="/" 
          class="mobile-nav-link"
          @click="closeMobileMenu"
        >
          首页
        </router-link>
        <router-link 
          to="/ai-roles" 
          class="mobile-nav-link"
          @click="closeMobileMenu"
        >
          AI角色
        </router-link>
        <router-link 
          v-if="authStore.isAuthenticated"
          to="/chat" 
          class="mobile-nav-link"
          @click="closeMobileMenu"
        >
          聊天
        </router-link>
        <router-link 
          v-if="authStore.isRegisteredUser"
          to="/history" 
          class="mobile-nav-link"
          @click="closeMobileMenu"
        >
          聊天历史
        </router-link>
        <router-link 
          to="/plans" 
          class="mobile-nav-link"
          @click="closeMobileMenu"
        >
          套餐选择
        </router-link>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/counter'
import UserMenu from '@/components/auth/UserMenu.vue'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const isMobile = ref(false)
const showMobileMenu = ref(false)

// 检查是否为移动端
function checkMobile() {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showMobileMenu.value = false
  }
}

// 切换移动端菜单
function toggleMobileMenu() {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
function closeMobileMenu() {
  showMobileMenu.value = false
}

// 处理窗口大小变化
function handleResize() {
  checkMobile()
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.app-header {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border-bottom: 1px solid var(--color-border-glass);
  position: sticky;
  top: 0;
  z-index: var(--z-index-navbar);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md) 0;
}

.brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  text-decoration: none;
  color: var(--color-text-primary);
  transition: color 0.2s ease;
}

.brand-link:hover {
  color: var(--color-accent-purple-light);
}

.brand-icon {
  width: 32px;
  height: 32px;
  color: var(--color-accent-purple);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  letter-spacing: -0.02em;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.nav-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--color-text-primary);
  background: var(--color-surface-primary);
}

.nav-link.active {
  color: var(--color-accent-purple);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--color-accent-purple);
  border-radius: 1px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.mobile-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
}

.mobile-nav {
  border-top: 1px solid var(--color-border-glass);
  padding: var(--space-md) 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  animation: fadeInUp 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-nav-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-md);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.router-link-active {
  color: var(--color-text-primary);
  background: var(--color-surface-primary);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .header-content {
    padding: var(--space-sm) 0;
  }
  
  .brand-text {
    font-size: var(--font-size-base);
  }
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
