import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { userAPI } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const chatSessions = ref([])
  const isLoading = ref(false)
  const isChangingPassword = ref(false)
  const error = ref('')

  // 计算属性
  const isGuest = computed(() => userInfo.value?.is_guest || false)
  const hasUserInfo = computed(() => !!userInfo.value)
  const hasChatSessions = computed(() => chatSessions.value.length > 0)

  // 格式化用户类型
  const userType = computed(() => {
    if (!userInfo.value) return ''
    return userInfo.value.is_guest ? '游客用户' : '注册用户'
  })

  // 格式化账户状态
  const accountStatus = computed(() => {
    if (!userInfo.value) return ''
    switch (userInfo.value.status) {
      case 'active':
        return '正常'
      case 'banned':
        return '已封禁'
      default:
        return userInfo.value.status
    }
  })

  // 方法
  function setUserInfo(info) {
    userInfo.value = info
  }

  function setChatSessions(sessions) {
    chatSessions.value = sessions
  }

  function setLoading(loading) {
    isLoading.value = loading
  }

  function setChangingPassword(changing) {
    isChangingPassword.value = changing
  }

  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = ''
  }

  // 加载用户信息
  async function loadUserInfo() {
    setLoading(true)
    clearError()

    try {
      const info = await userAPI.getCurrentUser()
      setUserInfo(info)
    } catch (err) {
      setError(err.message || '加载用户信息失败')
      setUserInfo(null)
    } finally {
      setLoading(false)
    }
  }

  // 加载聊天会话列表
  async function loadChatSessions() {
    setLoading(true)
    clearError()

    try {
      const sessions = await userAPI.getUserChatSessions()
      setChatSessions(Array.isArray(sessions) ? sessions : [])
    } catch (err) {
      setError(err.message || '加载聊天历史失败')
      setChatSessions([])
    } finally {
      setLoading(false)
    }
  }

  // 修改密码
  async function changePassword(oldPassword, newPassword) {
    if (!oldPassword || !newPassword) {
      setError('请填写完整的密码信息')
      return false
    }

    if (oldPassword === newPassword) {
      setError('新密码不能与旧密码相同')
      return false
    }

    if (newPassword.length < 6) {
      setError('新密码长度至少6个字符')
      return false
    }

    if (!/^(?=.*[a-zA-Z])(?=.*\d)/.test(newPassword)) {
      setError('新密码必须包含字母和数字')
      return false
    }

    setChangingPassword(true)
    clearError()

    try {
      await userAPI.changePassword(oldPassword, newPassword)
      return true
    } catch (err) {
      setError(err.message || '密码修改失败')
      return false
    } finally {
      setChangingPassword(false)
    }
  }

  // 刷新所有数据
  async function refreshData() {
    await Promise.all([
      loadUserInfo(),
      loadChatSessions()
    ])
  }

  // 清空所有数据
  function clearData() {
    setUserInfo(null)
    setChatSessions([])
    clearError()
  }

  return {
    // 状态
    userInfo,
    chatSessions,
    isLoading,
    isChangingPassword,
    error,

    // 计算属性
    isGuest,
    hasUserInfo,
    hasChatSessions,
    userType,
    accountStatus,

    // 方法
    setUserInfo,
    setChatSessions,
    setLoading,
    setChangingPassword,
    setError,
    clearError,
    loadUserInfo,
    loadChatSessions,
    changePassword,
    refreshData,
    clearData
  }
})
