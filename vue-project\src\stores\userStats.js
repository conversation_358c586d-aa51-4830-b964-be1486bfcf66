import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { userAPI } from '@/services/api'

export const useUserStatsStore = defineStore('userStats', () => {
  // 状态
  const stats = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)

  // 计算属性
  const totalSessions = computed(() => stats.value?.total_sessions || 0)
  const totalMessages = computed(() => stats.value?.total_messages || 0)
  const totalOrders = computed(() => stats.value?.total_orders || 0)
  const totalSpent = computed(() => stats.value?.total_spent || 0)
  const dailyMessageCount = computed(() => stats.value?.daily_message_count || 0)
  const messageCredits = computed(() => stats.value?.message_credits || 0)
  const isVip = computed(() => stats.value?.is_vip === true)
  const memberSince = computed(() => {
    if (!stats.value?.member_since) return null
    return new Date(stats.value.member_since)
  })

  // 格式化金额
  const formattedTotalSpent = computed(() => {
    return `¥${totalSpent.value.toFixed(2)}`
  })

  // 格式化注册时间
  const formattedMemberSince = computed(() => {
    if (!memberSince.value) return ''
    return memberSince.value.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  })

  // 计算会员天数
  const memberDays = computed(() => {
    if (!memberSince.value) return 0
    const now = new Date()
    const diffTime = now - memberSince.value
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  })

  // 方法
  async function loadStats() {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      const response = await userAPI.getStats()
      stats.value = response.data
      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err.response?.data?.message || '获取用户统计失败'
      console.error('Failed to load user stats:', err)
    } finally {
      isLoading.value = false
    }
  }

  function clearStats() {
    stats.value = null
    error.value = null
    lastUpdated.value = null
  }

  function clearError() {
    error.value = null
  }

  // 检查是否需要刷新数据（5分钟缓存）
  function shouldRefresh() {
    if (!lastUpdated.value) return true
    const now = new Date()
    const diffMinutes = (now - lastUpdated.value) / (1000 * 60)
    return diffMinutes > 5
  }

  // 获取统计数据（带缓存）
  async function getStats(forceRefresh = false) {
    if (forceRefresh || shouldRefresh()) {
      await loadStats()
    }
    return stats.value
  }

  // 更新特定统计字段
  function updateStats(newStats) {
    if (stats.value) {
      stats.value = { ...stats.value, ...newStats }
    } else {
      stats.value = newStats
    }
    lastUpdated.value = new Date()
  }

  // 增加消息计数
  function incrementMessageCount() {
    if (stats.value) {
      stats.value.total_messages = (stats.value.total_messages || 0) + 1
      stats.value.daily_message_count = (stats.value.daily_message_count || 0) + 1
    }
  }

  // 增加会话计数
  function incrementSessionCount() {
    if (stats.value) {
      stats.value.total_sessions = (stats.value.total_sessions || 0) + 1
    }
  }

  // 增加订单计数和消费金额
  function addOrder(amount) {
    if (stats.value) {
      stats.value.total_orders = (stats.value.total_orders || 0) + 1
      stats.value.total_spent = (stats.value.total_spent || 0) + amount
    }
  }

  return {
    // 状态
    stats,
    isLoading,
    error,
    lastUpdated,

    // 计算属性
    totalSessions,
    totalMessages,
    totalOrders,
    totalSpent,
    dailyMessageCount,
    messageCredits,
    isVip,
    memberSince,
    formattedTotalSpent,
    formattedMemberSince,
    memberDays,

    // 方法
    loadStats,
    clearStats,
    clearError,
    shouldRefresh,
    getStats,
    updateStats,
    incrementMessageCount,
    incrementSessionCount,
    addOrder
  }
})
