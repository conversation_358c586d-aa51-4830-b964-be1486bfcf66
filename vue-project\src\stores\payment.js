import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { paymentAPI, userAPI } from '@/services/api'

export const usePaymentStore = defineStore('payment', () => {
  // 状态
  const plans = ref([])
  const subscriptions = ref([])
  const orders = ref([])
  const selectedPlan = ref(null)
  const currentOrder = ref(null)
  const isLoading = ref(false)
  const isCreatingOrder = ref(false)
  const showPaymentModal = ref(false)
  const error = ref('')

  // 计算属性
  const hasPlans = computed(() => plans.value.length > 0)
  const hasSubscriptions = computed(() => subscriptions.value.length > 0)
  const hasOrders = computed(() => orders.value.length > 0)

  // 按类型分组的套餐
  const subscriptionPlans = computed(() => 
    plans.value.filter(plan => plan.plan_type === 'subscription')
  )
  
  const oneTimePlans = computed(() => 
    plans.value.filter(plan => plan.plan_type === 'one_time')
  )

  // 有效订阅
  const activeSubscriptions = computed(() => 
    subscriptions.value.filter(sub => sub.status === 'active')
  )

  // 方法
  function setPlans(planList) {
    plans.value = planList
  }

  function setSubscriptions(subList) {
    subscriptions.value = subList
  }

  function setOrders(orderList) {
    orders.value = orderList
  }

  function setSelectedPlan(plan) {
    selectedPlan.value = plan
  }

  function setCurrentOrder(order) {
    currentOrder.value = order
  }

  function setLoading(loading) {
    isLoading.value = loading
  }

  function setCreatingOrder(creating) {
    isCreatingOrder.value = creating
  }

  function setShowPaymentModal(show) {
    showPaymentModal.value = show
  }

  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = ''
  }

  // 加载套餐列表
  async function loadPlans() {
    setLoading(true)
    clearError()

    try {
      const planList = await paymentAPI.getPlans()
      setPlans(Array.isArray(planList) ? planList : [])
    } catch (err) {
      setError(err.message || '加载套餐失败')
      setPlans([])
    } finally {
      setLoading(false)
    }
  }

  // 加载用户订阅
  async function loadSubscriptions() {
    setLoading(true)
    clearError()

    try {
      const subList = await userAPI.getUserSubscriptions()
      setSubscriptions(Array.isArray(subList) ? subList : [])
    } catch (err) {
      setError(err.message || '加载订阅信息失败')
      setSubscriptions([])
    } finally {
      setLoading(false)
    }
  }

  // 加载订单历史
  async function loadOrders() {
    setLoading(true)
    clearError()

    try {
      const orderList = await userAPI.getUserOrders()
      setOrders(Array.isArray(orderList) ? orderList : [])
    } catch (err) {
      setError(err.message || '加载订单历史失败')
      setOrders([])
    } finally {
      setLoading(false)
    }
  }

  // 创建订单
  async function createOrder(planId) {
    if (!planId) {
      setError('请选择套餐')
      return false
    }

    setCreatingOrder(true)
    clearError()

    try {
      const order = await paymentAPI.createOrder(planId)
      setCurrentOrder(order)
      setShowPaymentModal(true)
      return true
    } catch (err) {
      setError(err.message || '创建订单失败')
      return false
    } finally {
      setCreatingOrder(false)
    }
  }

  // 模拟支付
  async function simulatePayment(status = 'success') {
    if (!currentOrder.value) {
      setError('没有当前订单')
      return false
    }

    setLoading(true)
    clearError()

    try {
      const result = await paymentAPI.simulatePayment(
        currentOrder.value.order_uuid,
        status
      )

      if (result === 'SUCCESS') {
        // 支付成功，刷新数据
        await Promise.all([
          loadSubscriptions(),
          loadOrders()
        ])
        
        // 关闭支付弹窗
        closePaymentModal()
        return true
      } else {
        setError('支付处理失败')
        return false
      }
    } catch (err) {
      setError(err.message || '支付失败')
      return false
    } finally {
      setLoading(false)
    }
  }

  // 关闭支付弹窗
  function closePaymentModal() {
    setShowPaymentModal(false)
    setCurrentOrder(null)
    setSelectedPlan(null)
  }

  // 刷新所有数据
  async function refreshData() {
    await Promise.all([
      loadPlans(),
      loadSubscriptions(),
      loadOrders()
    ])
  }

  // 清空所有数据
  function clearData() {
    setPlans([])
    setSubscriptions([])
    setOrders([])
    setSelectedPlan(null)
    setCurrentOrder(null)
    setShowPaymentModal(false)
    clearError()
  }

  // 格式化价格
  function formatPrice(price) {
    return `¥${parseFloat(price).toFixed(2)}`
  }

  // 格式化套餐类型
  function formatPlanType(planType) {
    const typeMap = {
      subscription: '订阅服务',
      one_time: '一次性购买'
    }
    return typeMap[planType] || planType
  }

  // 格式化订单状态
  function formatOrderStatus(status) {
    const statusMap = {
      pending: '待支付',
      completed: '已完成',
      failed: '支付失败',
      refunded: '已退款'
    }
    return statusMap[status] || status
  }

  // 格式化订阅状态
  function formatSubscriptionStatus(status) {
    const statusMap = {
      active: '有效',
      expired: '已过期',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  return {
    // 状态
    plans,
    subscriptions,
    orders,
    selectedPlan,
    currentOrder,
    isLoading,
    isCreatingOrder,
    showPaymentModal,
    error,

    // 计算属性
    hasPlans,
    hasSubscriptions,
    hasOrders,
    subscriptionPlans,
    oneTimePlans,
    activeSubscriptions,

    // 方法
    setPlans,
    setSubscriptions,
    setOrders,
    setSelectedPlan,
    setCurrentOrder,
    setLoading,
    setCreatingOrder,
    setShowPaymentModal,
    setError,
    clearError,
    loadPlans,
    loadSubscriptions,
    loadOrders,
    createOrder,
    simulatePayment,
    closePaymentModal,
    refreshData,
    clearData,
    formatPrice,
    formatPlanType,
    formatOrderStatus,
    formatSubscriptionStatus
  }
})
