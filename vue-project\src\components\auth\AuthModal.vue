<template>
  <Teleport to="body">
    <div v-if="isVisible" class="auth-modal-overlay" @click="handleOverlayClick">
      <div class="auth-modal" @click.stop>
        <button class="modal-close" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <div class="modal-content">
          <!-- 游客提示 -->
          <GuestPrompt
            v-if="currentView === 'guest'"
            @show-register="currentView = 'register'"
            @show-login="currentView = 'login'"
            @guest-created="handleAuthSuccess"
          />

          <!-- 登录表单 -->
          <LoginForm
            v-else-if="currentView === 'login'"
            @switch-to-register="currentView = 'register'"
            @login-success="handleAuthSuccess"
          />

          <!-- 注册表单 -->
          <RegisterForm
            v-else-if="currentView === 'register'"
            @switch-to-login="currentView = 'login'"
            @register-success="handleAuthSuccess"
          />
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, watch } from 'vue'
import GuestPrompt from './GuestPrompt.vue'
import LoginForm from './LoginForm.vue'
import RegisterForm from './RegisterForm.vue'

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  initialView: {
    type: String,
    default: 'guest', // 'guest', 'login', 'register'
    validator: (value) => ['guest', 'login', 'register'].includes(value)
  }
})

// 定义事件
const emit = defineEmits(['close', 'auth-success'])

// 响应式数据
const isVisible = ref(props.visible)
const currentView = ref(props.initialView)

// 监听props变化
watch(() => props.visible, (newValue) => {
  isVisible.value = newValue
  if (newValue) {
    currentView.value = props.initialView
    // 防止背景滚动
    document.body.style.overflow = 'hidden'
    // 确保模态框显示在正确位置
    document.body.style.position = 'relative'
  } else {
    // 恢复背景滚动
    document.body.style.overflow = ''
    document.body.style.position = ''
  }
})

watch(() => props.initialView, (newValue) => {
  currentView.value = newValue
})

// 关闭模态框
function closeModal() {
  console.log('AuthModal: closeModal called')
  isVisible.value = false
  document.body.style.overflow = ''
  document.body.style.position = ''
  emit('close')
}

// 处理点击遮罩层
function handleOverlayClick() {
  console.log('AuthModal: Overlay clicked, closing modal')
  closeModal()
}

// 处理认证成功
function handleAuthSuccess() {
  console.log('AuthModal: handleAuthSuccess called, closing modal')
  closeModal()
  emit('auth-success')
}

// 组件卸载时恢复滚动
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.body.style.overflow = ''
  document.body.style.position = ''
})
</script>

<style scoped>
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  /* 确保在所有浏览器中正确居中 */
  place-items: center;
  place-content: center;
  z-index: 9999;
  padding: var(--space-lg);
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保模态框在最顶层 */
  isolation: isolate;
  /* 防止被其他元素遮挡 */
  will-change: transform;
  /* 确保覆盖整个视口 */
  margin: 0;
  box-sizing: border-box;
}

.auth-modal {
  position: relative;
  background: var(--color-background-dark);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-glass);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  max-width: 500px;
  overflow-y: auto;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保模态框内容正确显示 */
  transform: translateZ(0);
  /* 确保模态框不会被推到屏幕外 */
  margin: auto;
  flex-shrink: 0;
}

.modal-close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  width: 32px;
  height: 32px;
  background: var(--color-surface-primary);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 1;
}

.modal-close:hover {
  background: var(--color-surface-secondary);
  color: var(--color-text-primary);
  transform: scale(1.05);
}

.modal-content {
  padding: var(--space-xl);
  padding-top: calc(var(--space-xl) + 32px + var(--space-lg)); /* 为关闭按钮留出空间 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-modal-overlay {
    padding: var(--space-md);
  }
  
  .modal-content {
    padding: var(--space-lg);
    padding-top: calc(var(--space-lg) + 32px + var(--space-md));
  }
  
  .modal-close {
    top: var(--space-md);
    right: var(--space-md);
  }
}

/* 滚动条样式 */
.auth-modal::-webkit-scrollbar {
  width: 6px;
}

.auth-modal::-webkit-scrollbar-track {
  background: var(--color-surface-primary);
  border-radius: 3px;
}

.auth-modal::-webkit-scrollbar-thumb {
  background: var(--color-surface-secondary);
  border-radius: 3px;
}

.auth-modal::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}
</style>
