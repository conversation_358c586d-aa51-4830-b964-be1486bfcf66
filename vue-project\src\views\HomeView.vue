<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import AuthModal from '@/components/auth/AuthModal.vue'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const authModalVisible = ref(false)
const authModalView = ref('guest')

// 处理URL查询参数中的认证请求
onMounted(() => {
  if (route.query.showAuth) {
    authModalView.value = route.query.showAuth === 'register' ? 'register' :
                         route.query.showAuth === 'login' ? 'login' : 'guest'
    authModalVisible.value = true
  }
})

// 显示认证模态框
function showAuthModal(view = 'guest') {
  authModalView.value = view
  authModalVisible.value = true
}

// 处理认证成功
function handleAuthSuccess() {
  authModalVisible.value = false

  // 如果有重定向URL，跳转到目标页面
  if (route.query.redirect) {
    router.push(route.query.redirect)
  } else {
    // 否则跳转到AI角色选择页面
    router.push('/ai-roles')
  }
}

// 关闭认证模态框
function handleAuthModalClose() {
  authModalVisible.value = false

  // 清除URL中的查询参数
  if (route.query.showAuth || route.query.redirect) {
    router.replace({ name: 'home' })
  }
}
</script>

<template>
  <div class="home-view">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              与AI伙伴分享你的
              <span class="highlight">内心世界</span>
            </h1>
            <p class="hero-description">
              在这个安全、私密的空间里，与不同性格的AI伙伴进行深度对话。
              无论是倾诉烦恼、寻求建议，还是探索内心，我们都在这里陪伴你。
            </p>
            <div class="hero-actions">
              <button
                v-if="!authStore.isAuthenticated"
                class="btn btn-primary btn-large"
                @click="showAuthModal('guest')"
              >
                立即开始聊天
              </button>
              <router-link
                v-else
                to="/ai-roles"
                class="btn btn-primary btn-large"
              >
                选择AI伙伴
              </router-link>
              <router-link
                to="/plans"
                class="btn btn-secondary btn-large"
              >
                查看定价
              </router-link>
              <router-link
                to="/ai-roles"
                class="btn btn-outline btn-large"
              >
                了解AI角色
              </router-link>
            </div>
          </div>
          <div class="hero-visual">
            <div class="floating-elements">
              <div class="floating-star star-1"></div>
              <div class="floating-star star-2"></div>
              <div class="floating-star star-3"></div>
              <div class="floating-star star-4"></div>
            </div>
            <div class="hero-card">
              <div class="chat-preview">
                <div class="chat-message user">
                  <div class="message-content">
                    今天工作压力好大，感觉有点迷茫...
                  </div>
                </div>
                <div class="chat-message assistant">
                  <div class="message-content">
                    我理解你的感受。工作压力确实会让人感到迷茫，这是很正常的。不如我们聊聊具体是什么让你感到压力？
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">为什么选择AI伙伴？</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L15.09 8.26L23 9L15.09 9.74L12 17L8.91 9.74L1 9L8.91 8.26L12 1Z" fill="currentColor"/>
              </svg>
            </div>
            <h3 class="feature-title">多样化AI角色</h3>
            <p class="feature-description">
              从温柔的心理顾问到博学的历史学家，每个AI都有独特的性格和专长，满足你不同的聊天需求。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
                <path d="M8 12L11 15L16 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">安全私密</h3>
            <p class="feature-description">
              你的对话内容完全私密，我们采用先进的加密技术保护你的隐私，让你可以放心地表达真实想法。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                <path d="M12 16L13.09 22.26L22 23L13.09 23.74L12 30L10.91 23.74L2 23L10.91 22.26L12 16Z" fill="currentColor" opacity="0.6"/>
              </svg>
            </div>
            <h3 class="feature-title">24/7 陪伴</h3>
            <p class="feature-description">
              无论何时何地，AI伙伴都在这里倾听你的声音。深夜的孤独、清晨的迷茫，我们都愿意陪伴。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 认证模态框 -->
    <AuthModal
      :visible="authModalVisible"
      :initial-view="authModalView"
      @close="handleAuthModalClose"
      @auth-success="handleAuthSuccess"
    />
  </div>
</template>

<style scoped>
.home-view {
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  padding: var(--space-3xl) 0;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
  min-height: 60vh;
}

.hero-text {
  max-width: 500px;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  letter-spacing: -0.02em;
}

.highlight {
  color: var(--color-accent-purple-light);
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: 2px;
  opacity: 0.6;
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
}

.hero-actions {
  display: flex;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.btn-large {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-base);
}

.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.floating-elements {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.floating-star {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--color-accent-purple);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.star-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.star-2 {
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.star-3 {
  bottom: 30%;
  left: 30%;
  animation-delay: 4s;
}

.star-4 {
  top: 10%;
  right: 10%;
  animation-delay: 1s;
}

.hero-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-glass);
  max-width: 400px;
  animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-preview {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.chat-message {
  display: flex;
  max-width: 80%;
}

.chat-message.user {
  align-self: flex-end;
}

.chat-message.assistant {
  align-self: flex-start;
}

.message-content {
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.chat-message.user .message-content {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
}

.chat-message.assistant .message-content {
  background: var(--color-surface-primary);
  color: var(--color-text-secondary);
}

/* 特色功能区域 */
.features-section {
  padding: var(--space-3xl) 0;
  background: linear-gradient(135deg, var(--color-background-darker) 0%, var(--color-background-dark) 100%);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  text-align: center;
  margin-bottom: var(--space-3xl);
  letter-spacing: -0.02em;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.feature-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-lg);
  color: var(--color-text-primary);
}

.feature-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.feature-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    text-align: center;
  }

  .hero-text {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: var(--space-xl) 0;
  }

  .hero-actions {
    justify-content: center;
  }

  .btn-large {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-sm);
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .hero-card {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 280px;
  }
}
</style>
