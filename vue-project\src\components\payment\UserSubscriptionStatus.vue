<template>
  <div class="subscription-status-card">
    <div class="card-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="card-title">我的订阅</h2>
          <p class="card-subtitle">查看您的会员订阅状态</p>
        </div>
        <div class="header-actions">
          <button
            class="btn btn-secondary btn-sm"
            @click="refreshUserInfo"
            :disabled="isLoading"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 15C19.9828 16.6224 19.0209 18.0668 17.7422 19.1573C16.4635 20.2477 14.9274 20.9385 13.3 21.15C11.6726 21.3615 10.0284 21.0906 8.56822 20.3712C7.10803 19.6517 5.90 18.5168 5.09 17.1C4.28 15.6832 3.90578 14.0479 4.01 12.4C4.11422 10.7521 4.69234 9.17254 5.68 7.84C6.66766 6.50746 8.01834 5.47776 9.57 4.86C11.1217 4.24224 12.8248 4.06768 14.49 4.36" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <div class="card-content">
      <!-- 加载状态 -->
      <div v-if="isLoading && !authStore.isVip" class="loading-content">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <p>正在加载订阅信息...</p>
      </div>

      <!-- VIP会员订阅信息 -->
      <div v-else-if="authStore.isVip && authStore.subscriptionInfo" class="subscriptions-list">
        <div class="subscription-item">
          <div class="subscription-info">
            <div class="subscription-header">
              <h3 class="subscription-name">{{ authStore.subscriptionInfo.plan_name }}</h3>
              <span class="subscription-status" :class="statusClass">
                {{ statusText }}
              </span>
            </div>
            <div class="subscription-details">
              <div class="detail-item">
                <span class="detail-label">开始时间</span>
                <span class="detail-value">{{ formatTime(authStore.subscriptionInfo.start_date) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">结束时间</span>
                <span class="detail-value">{{ formatTime(authStore.subscriptionInfo.end_date) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">剩余时间</span>
                <span class="detail-value">{{ formatRemainingTime(authStore.subscriptionInfo.end_date) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">剩余天数</span>
                <span class="detail-value">{{ authStore.subscriptionRemainingDays }}天</span>
              </div>
            </div>
          </div>
          <div class="subscription-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: subscriptionProgress + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ subscriptionProgress }}% 已使用</span>
          </div>
        </div>
      </div>

      <!-- 非VIP用户状态 -->
      <div v-else-if="!authStore.isVip" class="empty-content">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="empty-title">您还不是VIP会员</h3>
        <p class="empty-description">升级为VIP会员，享受无限制的AI聊天体验和专属功能</p>
        <div class="empty-actions">
          <router-link to="/plans" class="btn btn-primary">
            查看套餐
          </router-link>
          <button class="btn btn-secondary" @click="refreshUserInfo">
            刷新状态
          </button>
        </div>
      </div>

      <!-- VIP但无订阅信息 -->
      <div v-else-if="authStore.isVip && !authStore.subscriptionInfo" class="warning-content">
        <div class="warning-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="warning-title">订阅信息异常</h3>
        <p class="warning-description">您是VIP会员，但订阅详情加载失败</p>
        <button class="btn btn-primary" @click="refreshUserInfo">
          重新加载
        </button>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ error }}</p>
        <button class="btn btn-primary" @click="refreshUserInfo">
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/counter'
import { userAPI } from '@/services/api'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const error = ref(null)

// 计算属性
const statusText = computed(() => {
  if (!authStore.isVip) return '非会员'

  switch (authStore.subscriptionStatus) {
    case 'active':
      return '有效'
    case 'expired':
      return '已过期'
    case 'cancelled':
      return '已取消'
    default:
      return '未知状态'
  }
})

const statusClass = computed(() => {
  if (!authStore.isVip) return 'status-none'

  switch (authStore.subscriptionStatus) {
    case 'active':
      return 'status-active'
    case 'expired':
      return 'status-expired'
    case 'cancelled':
      return 'status-cancelled'
    default:
      return 'status-none'
  }
})

const subscriptionProgress = computed(() => {
  if (!authStore.subscriptionInfo) return 0

  const startDate = new Date(authStore.subscriptionInfo.start_date)
  const endDate = new Date(authStore.subscriptionInfo.end_date)
  const now = new Date()

  const totalDuration = endDate - startDate
  const usedDuration = now - startDate

  if (totalDuration <= 0) return 100

  const progress = (usedDuration / totalDuration) * 100
  return Math.min(Math.max(progress, 0), 100).toFixed(1)
})

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '未知'

  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化剩余时间
function formatRemainingTime(endTime) {
  if (!endTime) return '未知'

  const end = new Date(endTime)
  const now = new Date()
  const diff = end - now

  if (diff <= 0) {
    return '已过期'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 刷新用户信息
async function refreshUserInfo() {
  if (isLoading.value) return

  isLoading.value = true
  error.value = null

  try {
    // 调用新的用户信息API
    const response = await userAPI.getProfile()

    // 更新authStore中的用户信息
    authStore.setUser(response.data)

    console.log('用户信息已刷新:', response.data)
  } catch (err) {
    error.value = err.response?.data?.message || '刷新用户信息失败，请稍后重试'
    console.error('Failed to refresh user info:', err)
  } finally {
    isLoading.value = false
  }
}

// 清除错误
function clearError() {
  error.value = null
}

// 组件挂载时初始化
onMounted(async () => {
  // 如果用户已认证但没有会员信息，则刷新用户信息
  if (authStore.isAuthenticated && !authStore.user) {
    await refreshUserInfo()
  }

  // 如果用户是VIP但没有订阅详情，也刷新一下
  if (authStore.isAuthenticated && authStore.isVip && !authStore.subscriptionInfo) {
    await refreshUserInfo()
  }
})
</script>

<style scoped>
.subscription-status-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all 0.3s ease;
}

.subscription-status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

/* 卡片头部 */
.card-header {
  background: var(--color-surface-primary);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

/* 卡片内容 */
.card-content {
  padding: var(--space-lg);
}

/* 订阅列表 */
.subscriptions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.subscription-item {
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  padding: var(--space-lg);
  transition: all 0.2s ease;
}

.subscription-item:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-purple);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.subscription-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.subscription-status {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.subscription-status.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.subscription-status.expired {
  background: rgba(244, 67, 54, 0.2);
  color: var(--color-error);
}

.subscription-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.detail-value {
  font-size: var(--font-size-xs);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

/* 进度条 */
.subscription-progress {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.progress-bar {
  height: 6px;
  background: var(--color-surface-primary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-accent-purple), var(--color-accent-yellow));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  text-align: right;
}

/* 空状态、错误状态和警告状态 */
.empty-content, .error-content, .warning-content, .loading-content {
  text-align: center;
  padding: var(--space-3xl);
}

.empty-icon, .error-icon, .warning-icon {
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-icon {
  color: var(--color-text-muted);
}

.error-icon {
  color: var(--color-error);
}

.warning-icon {
  color: var(--color-warning);
}

.empty-title, .error-title, .warning-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.empty-description, .error-message, .warning-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-normal);
}

.empty-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.empty-actions .btn {
  min-width: 120px;
}

.loading-content p {
  color: var(--color-text-secondary);
  margin-top: var(--space-md);
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subscription-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .subscription-status {
    align-self: flex-end;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .header-actions {
    display: none;
  }
}
</style>
