import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/counter'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: {
        title: '首页 - AI伙伴聊天'
      }
    },
    {
      path: '/ai-roles',
      name: 'ai-roles',
      component: () => import('../views/AIRolesView.vue'),
      meta: {
        title: 'AI角色选择 - AI伙伴聊天'
      }
    },
    {
      path: '/chat/:sessionId?',
      name: 'chat',
      component: () => import('../views/ChatView.vue'),
      meta: {
        title: '聊天 - AI伙伴聊天',
        requiresAuth: true
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: {
        title: '个人中心 - AI伙伴聊天',
        requiresAuth: true,
        requiresRegistered: true
      }
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('../views/HistoryView.vue'),
      meta: {
        title: '聊天历史 - AI伙伴聊天',
        requiresAuth: true,
        requiresRegistered: true
      }
    },
    {
      path: '/plans',
      name: 'plans',
      component: () => import('../views/PlansView.vue'),
      meta: {
        title: '套餐选择 - AI伙伴聊天'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue'),
      meta: {
        title: '页面未找到 - AI伙伴聊天'
      }
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 初始化认证状态
  if (!authStore.user && authStore.token) {
    authStore.initializeAuth()
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 未认证，重定向到首页并显示认证模态框
      next({
        name: 'home',
        query: {
          showAuth: 'true',
          redirect: to.fullPath
        }
      })
      return
    }

    // 检查是否需要注册用户
    if (to.meta.requiresRegistered && authStore.isGuest) {
      // 游客用户访问需要注册的页面，重定向到首页并显示注册模态框
      next({
        name: 'home',
        query: {
          showAuth: 'register',
          redirect: to.fullPath
        }
      })
      return
    }
  }

  next()
})

export default router
