<template>
  <div 
    class="plan-card"
    :class="{ 
      'selected': selected,
      'subscription': plan.plan_type === 'subscription',
      'one-time': plan.plan_type === 'one_time'
    }"
    @click="$emit('select', plan)"
  >
    <!-- 套餐类型标签 -->
    <div class="plan-type-badge">
      <svg v-if="plan.plan_type === 'subscription'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
      </svg>
      <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        <path d="M12 8V16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path d="M8 10V14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path d="M16 10V14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
      {{ formatPlanType(plan.plan_type) }}
    </div>

    <!-- 套餐名称 -->
    <div class="plan-header">
      <h3 class="plan-name">{{ plan.name }}</h3>
      <div class="plan-price">
        <span class="price-symbol">¥</span>
        <span class="price-amount">{{ displayPrice }}</span>
        <span v-if="plan.plan_type === 'subscription'" class="price-period">
          /{{ displayPeriod }}
        </span>
      </div>
    </div>

    <!-- 套餐描述 -->
    <div class="plan-description">
      <p>{{ plan.description }}</p>
    </div>

    <!-- 套餐特性 -->
    <div class="plan-features">
      <div class="feature-item">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span v-if="plan.plan_type === 'subscription'">
          无限制聊天消息
        </span>
        <span v-else>
          {{ plan.message_credits }} 条消息
        </span>
      </div>

      <div v-if="plan.plan_type === 'subscription'" class="feature-item">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>所有AI角色访问</span>
      </div>

      <div v-if="plan.plan_type === 'subscription'" class="feature-item">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>优先客服支持</span>
      </div>

      <div class="feature-item">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span v-if="plan.plan_type === 'subscription'">
          有效期 {{ plan.duration_days }} 天
        </span>
        <span v-else>
          永久有效
        </span>
      </div>
    </div>

    <!-- 选中指示器 -->
    <div v-if="selected" class="selected-indicator">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>

    <!-- 购买按钮 -->
    <button
      class="plan-purchase-btn"
      :class="{ selected: selected }"
      @click.stop="$emit('select', plan)"
    >
      {{ selected ? '已选择' : '选择套餐' }}
    </button>

    <!-- 推荐标签 -->
    <div v-if="isRecommended" class="recommended-badge">
      推荐
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  plan: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  billingCycle: {
    type: String,
    default: 'monthly'
  }
})

// Emits
defineEmits(['select'])

// 计算属性
const isRecommended = computed(() => {
  // 可以根据业务逻辑判断哪个套餐是推荐的
  return props.plan.name.includes('月度') || props.plan.name.includes('年度')
})

const displayPrice = computed(() => {
  if (props.plan.plan_type === 'subscription' && props.billingCycle === 'yearly') {
    // 年付价格 = 月价格 * 12 * 0.8 (20% 折扣)
    const yearlyPrice = parseFloat(props.plan.price) * 12 * 0.8
    return Math.floor(yearlyPrice)
  }
  return formatPrice(props.plan.price)
})

const displayPeriod = computed(() => {
  if (props.plan.plan_type === 'subscription') {
    return props.billingCycle === 'yearly' ? '年' : '月'
  }
  return ''
})

// 方法
function formatPrice(price) {
  return parseFloat(price).toFixed(0)
}

function formatPlanType(planType) {
  const typeMap = {
    subscription: '订阅服务',
    one_time: '消息包'
  }
  return typeMap[planType] || planType
}

function formatDuration(days) {
  if (days >= 365) {
    return `${Math.floor(days / 365)}年`
  } else if (days >= 30) {
    return `${Math.floor(days / 30)}个月`
  } else {
    return `${days}天`
  }
}
</script>

<style scoped>
.plan-card {
  position: relative;
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 2px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-xl);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.plan-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
  border-color: var(--color-accent-purple);
}

.plan-card.selected {
  border-color: var(--color-accent-yellow);
  background: rgba(255, 214, 10, 0.05);
  box-shadow: var(--shadow-glass), var(--shadow-glow-yellow);
}

.plan-card.subscription {
  background: linear-gradient(135deg, 
    rgba(155, 89, 182, 0.1) 0%, 
    rgba(155, 89, 182, 0.05) 100%);
}

.plan-card.one-time {
  background: linear-gradient(135deg, 
    rgba(255, 214, 10, 0.1) 0%, 
    rgba(255, 214, 10, 0.05) 100%);
}

/* 套餐类型标签 */
.plan-type-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  background: var(--color-surface-secondary);
  color: var(--color-text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-lg);
}

.subscription .plan-type-badge {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
}

.one-time .plan-type-badge {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
}

/* 套餐头部 */
.plan-header {
  text-align: center;
  margin-bottom: var(--space-lg);
}

.plan-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-xs);
}

.price-symbol {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.price-amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-yellow);
  line-height: 1;
}

.price-period {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 套餐描述 */
.plan-description {
  margin-bottom: var(--space-lg);
}

.plan-description p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  text-align: center;
  margin: 0;
}

/* 套餐特性 */
.plan-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  flex: 1;
  margin-bottom: var(--space-lg);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.feature-item svg {
  color: var(--color-accent-yellow);
  flex-shrink: 0;
}

/* 选中指示器 */
.selected-indicator {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  width: 32px;
  height: 32px;
  background: var(--color-accent-yellow);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-background-dark);
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 购买按钮 */
.plan-purchase-btn {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: auto;
}

.plan-purchase-btn:hover {
  background: var(--color-accent-purple);
  border-color: var(--color-accent-purple);
  transform: translateY(-1px);
}

.plan-purchase-btn.selected {
  background: var(--color-accent-yellow);
  border-color: var(--color-accent-yellow);
  color: var(--color-background-dark);
}

/* 推荐标签 */
.recommended-badge {
  position: absolute;
  top: -1px;
  right: var(--space-lg);
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-yellow));
  color: var(--color-text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-glass);
}

/* 动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-card {
    padding: var(--space-lg);
  }

  .plan-name {
    font-size: var(--font-size-base);
  }

  .price-amount {
    font-size: var(--font-size-xl);
  }
}
</style>
