<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块三 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input, textarea {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        .session-item {
            background: #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #9B59B6;
        }
        .message-item {
            background: #444;
            padding: 8px;
            margin: 3px 0;
            border-radius: 4px;
        }
        .user-message {
            background: #9B59B6;
        }
        .assistant-message {
            background: #2E7D32;
        }
    </style>
</head>
<body>
    <h1>模块三 API 测试 - 核心对话功能</h1>
    
    <div class="test-section">
        <h2>1. 用户认证</h2>
        <input type="email" id="auth-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="auth-password" placeholder="密码" value="password123">
        <button onclick="testLogin()">登录获取Token</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 创建聊天会话</h2>
        <input type="number" id="role-id" placeholder="AI角色ID" value="1" min="1">
        <button onclick="testCreateSession()">创建会话</button>
        <div id="session-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 获取会话列表</h2>
        <button onclick="testGetSessions()">获取会话列表</button>
        <div id="sessions-result" class="result"></div>
        <div id="sessions-display"></div>
    </div>

    <div class="test-section">
        <h2>4. 发送消息</h2>
        <input type="text" id="session-uuid" placeholder="会话UUID">
        <textarea id="message-content" placeholder="消息内容" rows="3">你好，我想了解一下历史</textarea>
        <br>
        <button onclick="testSendMessage()">发送消息</button>
        <button onclick="testSendMessageStream()">发送消息(流式)</button>
        <div id="message-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 获取会话消息</h2>
        <input type="text" id="get-session-uuid" placeholder="会话UUID">
        <button onclick="testGetMessages()">获取消息历史</button>
        <div id="messages-result" class="result"></div>
        <div id="messages-display"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';
        let currentSessionUuid = '';

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        function displaySessions(sessions) {
            const container = document.getElementById('sessions-display');
            if (!sessions || sessions.length === 0) {
                container.innerHTML = '<p>没有找到会话</p>';
                return;
            }

            container.innerHTML = sessions.map(session => `
                <div class="session-item">
                    <strong>${session.title}</strong><br>
                    AI角色: ${session.ai_role_name}<br>
                    UUID: ${session.uuid}<br>
                    创建时间: ${new Date(session.created_at).toLocaleString()}
                </div>
            `).join('');
        }

        function displayMessages(messages) {
            const container = document.getElementById('messages-display');
            if (!messages || messages.length === 0) {
                container.innerHTML = '<p>没有找到消息</p>';
                return;
            }

            container.innerHTML = messages.map(message => `
                <div class="message-item ${message.role}-message">
                    <strong>${message.role}:</strong> ${message.content}<br>
                    <small>${new Date(message.created_at).toLocaleString()}</small>
                </div>
            `).join('');
        }

        async function testLogin() {
            const email = document.getElementById('auth-email').value;
            const password = document.getElementById('auth-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = data.data.token;
                    displayResult('auth-result', data);
                } else {
                    displayResult('auth-result', data, true);
                }
            } catch (error) {
                displayResult('auth-result', { error: error.message }, true);
            }
        }

        async function testCreateSession() {
            const roleId = document.getElementById('role-id').value;
            
            if (!currentToken) {
                displayResult('session-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ai_role_id: parseInt(roleId) })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 201) {
                    currentSessionUuid = data.data.uuid;
                    document.getElementById('session-uuid').value = currentSessionUuid;
                    document.getElementById('get-session-uuid').value = currentSessionUuid;
                    displayResult('session-result', data);
                } else {
                    displayResult('session-result', data, true);
                }
            } catch (error) {
                displayResult('session-result', { error: error.message }, true);
            }
        }

        async function testGetSessions() {
            if (!currentToken) {
                displayResult('sessions-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('sessions-result', data);
                    displaySessions(data.data);
                } else {
                    displayResult('sessions-result', data, true);
                }
            } catch (error) {
                displayResult('sessions-result', { error: error.message }, true);
            }
        }

        async function testSendMessage() {
            const sessionUuid = document.getElementById('session-uuid').value;
            const content = document.getElementById('message-content').value;
            
            if (!currentToken) {
                displayResult('message-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            if (!sessionUuid) {
                displayResult('message-result', { error: '请输入会话UUID' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 201) {
                    displayResult('message-result', data);
                } else {
                    displayResult('message-result', data, true);
                }
            } catch (error) {
                displayResult('message-result', { error: error.message }, true);
            }
        }

        async function testSendMessageStream() {
            const sessionUuid = document.getElementById('session-uuid').value;
            const content = document.getElementById('message-content').value;
            
            if (!currentToken) {
                displayResult('message-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            if (!sessionUuid) {
                displayResult('message-result', { error: '请输入会话UUID' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages/stream`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let streamResult = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop();

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                displayResult('message-result', { stream_complete: true, content: streamResult });
                                return;
                            }

                            try {
                                const event = JSON.parse(data);
                                streamResult += event.content || '';
                                displayResult('message-result', { streaming: true, content: streamResult });
                            } catch (parseError) {
                                console.warn('解析SSE事件失败:', parseError);
                            }
                        }
                    }
                }
            } catch (error) {
                displayResult('message-result', { error: error.message }, true);
            }
        }

        async function testGetMessages() {
            const sessionUuid = document.getElementById('get-session-uuid').value;
            
            if (!currentToken) {
                displayResult('messages-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            if (!sessionUuid) {
                displayResult('messages-result', { error: '请输入会话UUID' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('messages-result', data);
                    displayMessages(data.data);
                } else {
                    displayResult('messages-result', data, true);
                }
            } catch (error) {
                displayResult('messages-result', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
