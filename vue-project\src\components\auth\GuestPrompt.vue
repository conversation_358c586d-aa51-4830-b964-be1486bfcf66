<template>
  <div class="guest-prompt">
    <div class="prompt-content">
      <div class="prompt-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H5V21H19V9Z" fill="currentColor"/>
        </svg>
      </div>
      
      <div class="prompt-text">
        <h3 class="text-lg font-semibold text-primary">您正在以游客身份访问</h3>
        <p class="text-sm text-secondary">
          游客模式下您可以立即开始与AI伙伴对话，但聊天记录不会被保存。
          注册账户后可以保存所有对话历史，随时回顾您的聊天内容。
        </p>
      </div>
    </div>

    <div class="prompt-actions">
      <button 
        class="btn btn-primary"
        @click="$emit('show-register')"
        :disabled="isLoading"
      >
        立即注册
      </button>
      
      <button 
        class="btn btn-secondary"
        @click="$emit('show-login')"
        :disabled="isLoading"
      >
        已有账户
      </button>
      
      <button 
        class="continue-as-guest"
        @click="handleContinueAsGuest"
        :disabled="isLoading"
      >
        <span v-if="isLoading" class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </span>
        <span v-else>继续游客模式</span>
      </button>
    </div>

    <div v-if="error" class="error-alert">
      {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/counter'
import { authAPI } from '@/services/api'

// 定义事件
const emit = defineEmits(['show-register', 'show-login', 'guest-created'])

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const error = ref('')

// 处理继续游客模式
async function handleContinueAsGuest() {
  isLoading.value = true
  error.value = ''

  try {
    const response = await authAPI.createGuest()
    
    // 保存游客用户信息和token
    authStore.setUser(response.user)
    authStore.setToken(response.token)
    authStore.saveUserToStorage()
    
    emit('guest-created')
  } catch (err) {
    error.value = err.message || '创建游客账户失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.guest-prompt {
  width: 100%;
  max-width: 480px;
  padding: var(--space-xl);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-glass);
}

.prompt-content {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.prompt-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
}

.prompt-text {
  flex: 1;
}

.prompt-text h3 {
  margin-bottom: var(--space-sm);
}

.prompt-text p {
  line-height: var(--line-height-normal);
}

.prompt-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.continue-as-guest {
  background: none;
  border: none;
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: var(--space-sm);
  text-decoration: underline;
  transition: color 0.2s ease;
  align-self: center;
}

.continue-as-guest:hover {
  color: var(--color-text-secondary);
}

.continue-as-guest:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.error-alert {
  margin-top: var(--space-lg);
  padding: var(--space-md);
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--color-error);
  border-radius: var(--border-radius-sm);
  color: var(--color-error);
  font-size: var(--font-size-sm);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .guest-prompt {
    padding: var(--space-lg);
  }
  
  .prompt-content {
    flex-direction: column;
    text-align: center;
  }
  
  .prompt-icon {
    align-self: center;
  }
}
</style>
