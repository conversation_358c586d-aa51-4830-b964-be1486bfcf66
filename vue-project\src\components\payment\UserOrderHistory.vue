<template>
  <div class="order-history-card">
    <div class="card-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="10" y1="9" x2="8" y2="9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="card-title">订单历史</h2>
          <p class="card-subtitle">
            查看您的购买记录
            <span v-if="userStatsStore.totalOrders > 0" class="stats-info">
              · 共{{ userStatsStore.totalOrders }}笔订单 · 累计消费{{ userStatsStore.formattedTotalSpent }}
            </span>
          </p>
        </div>
        <div class="header-actions">
          <button 
            class="btn btn-secondary btn-sm"
            @click="paymentStore.loadOrders()"
            :disabled="paymentStore.isLoading"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 15C19.9828 16.6224 19.0209 18.0668 17.7422 19.1573C16.4635 20.2477 14.9274 20.9385 13.3 21.15C11.6726 21.3615 10.0284 21.0906 8.56822 20.3712C7.10803 19.6517 5.90 18.5168 5.09 17.1C4.28 15.6832 3.90578 14.0479 4.01 12.4C4.11422 10.7521 4.69234 9.17254 5.68 7.84C6.66766 6.50746 8.01834 5.47776 9.57 4.86C11.1217 4.24224 12.8248 4.06768 14.49 4.36" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <div class="card-content">
      <!-- 加载状态 -->
      <div v-if="paymentStore.isLoading && !paymentStore.hasOrders" class="loading-content">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <p>正在加载订单历史...</p>
      </div>

      <!-- 订单列表 -->
      <div v-else-if="paymentStore.hasOrders" class="orders-list">
        <div
          v-for="order in paymentStore.orders"
          :key="order.order_uuid"
          class="order-item"
        >
          <div class="order-info">
            <div class="order-header">
              <div class="order-id">
                <span class="order-label">订单号</span>
                <span class="order-value">{{ formatOrderId(order.order_uuid) }}</span>
              </div>
              <span class="order-status" :class="order.status">
                {{ paymentStore.formatOrderStatus(order.status) }}
              </span>
            </div>
            
            <div class="order-details">
              <div class="detail-row">
                <span class="detail-label">套餐名称</span>
                <span class="detail-value">{{ order.plan_name }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">支付金额</span>
                <span class="detail-value price">{{ paymentStore.formatPrice(order.amount) }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">创建时间</span>
                <span class="detail-value">{{ formatTime(order.created_at) }}</span>
              </div>
              <div v-if="order.completed_at" class="detail-row">
                <span class="detail-label">完成时间</span>
                <span class="detail-value">{{ formatTime(order.completed_at) }}</span>
              </div>
            </div>
          </div>

          <!-- 订单操作 -->
          <div class="order-actions">
            <button 
              v-if="order.status === 'pending'"
              class="btn btn-primary btn-sm"
              @click="continuePayment(order)"
            >
              继续支付
            </button>
            <button 
              v-if="order.status === 'completed'"
              class="btn btn-secondary btn-sm"
              @click="viewOrderDetails(order)"
            >
              查看详情
            </button>
          </div>
        </div>

        <!-- 分页或加载更多 -->
        <div v-if="paymentStore.orders.length >= 10" class="load-more">
          <button class="btn btn-secondary" @click="loadMoreOrders">
            加载更多
          </button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-content">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无订单记录</h3>
        <p class="empty-description">购买套餐后，您的订单记录将显示在这里</p>
        <button class="btn btn-primary" @click="scrollToPlans">
          立即购买
        </button>
      </div>

      <!-- 错误状态 -->
      <div v-if="paymentStore.error && !paymentStore.hasOrders" class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ paymentStore.error }}</p>
        <button class="btn btn-primary" @click="paymentStore.loadOrders()">
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { usePaymentStore } from '@/stores/payment'
import { useUserStatsStore } from '@/stores/userStats'
import { useAuthStore } from '@/stores/counter'

// 状态管理
const paymentStore = usePaymentStore()
const userStatsStore = useUserStatsStore()
const authStore = useAuthStore()

// 格式化订单ID
function formatOrderId(uuid) {
  if (!uuid) return ''
  return uuid.substring(0, 8).toUpperCase()
}

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '未知'
  
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 继续支付
function continuePayment(order) {
  // 设置当前订单并打开支付弹窗
  paymentStore.setCurrentOrder(order)
  paymentStore.setShowPaymentModal(true)
}

// 查看订单详情
function viewOrderDetails(order) {
  // 这里可以实现订单详情查看逻辑
  console.log('查看订单详情:', order)
}

// 加载更多订单
function loadMoreOrders() {
  // 这里可以实现分页加载逻辑
  console.log('加载更多订单')
}

// 滚动到套餐区域
function scrollToPlans() {
  const plansSection = document.querySelector('.plans-section')
  if (plansSection) {
    plansSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// 组件挂载时初始化
onMounted(async () => {
  if (authStore.isAuthenticated) {
    // 加载用户统计信息
    await userStatsStore.loadStats()

    // 加载订单历史
    await paymentStore.loadOrders()
  }
})
</script>

<style scoped>
.order-history-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all 0.3s ease;
}

.order-history-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

/* 卡片头部 */
.card-header {
  background: var(--color-surface-primary);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--color-accent-yellow);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-background-dark);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.stats-info {
  color: var(--color-accent-purple);
  font-weight: var(--font-weight-medium);
}

.header-actions {
  display: flex;
  gap: var(--space-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

/* 卡片内容 */
.card-content {
  padding: var(--space-lg);
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-lg);
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  padding: var(--space-lg);
  transition: all 0.2s ease;
}

.order-item:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-yellow);
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
  gap: var(--space-md);
}

.order-id {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.order-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  font-weight: var(--font-weight-medium);
}

.order-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
  font-family: monospace;
}

.order-status {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
}

.order-status.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.order-status.completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.order-status.failed {
  background: rgba(244, 67, 54, 0.2);
  color: var(--color-error);
}

.order-status.refunded {
  background: rgba(156, 39, 176, 0.2);
  color: #9C27B0;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.detail-value {
  font-size: var(--font-size-xs);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  text-align: right;
}

.detail-value.price {
  font-size: var(--font-size-sm);
  color: var(--color-accent-yellow);
  font-weight: var(--font-weight-semibold);
}

/* 订单操作 */
.order-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  flex-shrink: 0;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border-glass);
}

/* 空状态和错误状态 */
.empty-content, .error-content, .loading-content {
  text-align: center;
  padding: var(--space-3xl);
}

.empty-icon, .error-icon {
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-icon {
  color: var(--color-text-muted);
}

.error-icon {
  color: var(--color-error);
}

.empty-title, .error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.empty-description, .error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-normal);
}

.loading-content p {
  color: var(--color-text-secondary);
  margin-top: var(--space-md);
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .order-status {
    align-self: flex-end;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .detail-value {
    text-align: left;
  }

  .order-actions {
    flex-direction: row;
    justify-content: flex-end;
  }

  .header-actions {
    display: none;
  }
}
</style>
