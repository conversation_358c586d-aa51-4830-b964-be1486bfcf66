<template>
  <div class="ai-roles-view">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">选择你的AI伙伴</h1>
        <p class="page-description">
          每个AI伙伴都有独特的性格和专长，选择最适合你的那一个开始对话
        </p>

        <!-- 搜索和筛选 -->
        <div class="search-section">
          <div class="search-bar">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索AI角色..."
              class="search-input"
            />
          </div>

          <div class="filter-tabs">
            <button
              v-for="category in categories"
              :key="category.key"
              class="filter-tab"
              :class="{ active: selectedCategory === category.key }"
              @click="selectedCategory = category.key"
            >
              {{ category.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载AI角色...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ error }}</p>
        <button class="btn btn-primary" @click="loadAIRoles">
          重试
        </button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="aiRoles.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor" opacity="0.3"/>
            <path d="M12 16L13.09 22.26L22 23L13.09 23.74L12 30L10.91 23.74L2 23L10.91 22.26L12 16Z" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
        <h3 class="empty-title">暂无AI角色</h3>
        <p class="empty-description">目前还没有可用的AI角色，请稍后再试</p>
        <button class="btn btn-primary" @click="loadAIRoles">
          重新加载
        </button>
      </div>

      <!-- AI角色列表 -->
      <div v-else class="roles-content">
        <!-- 角色统计 -->
        <div class="roles-stats">
          <span class="stats-text">
            找到 <strong>{{ filteredRoles.length }}</strong> 个AI角色
          </span>
        </div>

        <div class="roles-list">
          <div
            v-for="role in filteredRoles"
            :key="role.id"
            class="role-card"
            @click="handleRoleClick(role)"
          >
            <div class="role-avatar">
              <img
                v-if="role.avatar_url"
                :src="role.avatar_url"
                :alt="role.name"
                class="avatar-image"
                @error="handleImageError"
              />
              <div v-else class="avatar-placeholder">
                <span class="avatar-initial">{{ role.name.charAt(0) }}</span>
              </div>
              <div class="avatar-glow"></div>
            </div>

            <div class="role-content">
              <div class="role-header">
                <h3 class="role-name">{{ role.name }}</h3>
                <div class="role-badge">AI伙伴</div>
              </div>
              <p class="role-description">{{ role.description }}</p>
            </div>

            <div class="role-actions">
              <button class="btn-start-chat" @click.stop="selectRole(role)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                开始聊天
              </button>
              <button class="btn-view-details" @click.stop="showRoleDetails(role)">
                查看详情
              </button>
            </div>
          </div>
        </div>

        <!-- 没有搜索结果 -->
        <div v-if="filteredRoles.length === 0 && searchQuery" class="no-results">
          <div class="no-results-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="no-results-title">未找到匹配的AI角色</h3>
          <p class="no-results-description">
            尝试使用不同的关键词或选择其他分类
          </p>
          <button class="btn btn-secondary" @click="clearSearch">
            清除搜索
          </button>
        </div>
      </div>

      <!-- 角色详情模态框 -->
      <RoleDetailModal
        :visible="detailModalVisible"
        :role="selectedRoleForDetail"
        @close="detailModalVisible = false"
        @start-chat="handleStartChatFromDetail"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { aiRoleAPI, chatAPI } from '@/services/api'
import RoleDetailModal from '@/components/ai-roles/RoleDetailModal.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const aiRoles = ref([])
const isLoading = ref(false)
const error = ref('')
const detailModalVisible = ref(false)
const selectedRoleForDetail = ref(null)
const searchQuery = ref('')
const selectedCategory = ref('all')

// 分类选项
const categories = ref([
  { key: 'all', label: '全部' },
  { key: 'assistant', label: '助手' },
  { key: 'creative', label: '创意' },
  { key: 'education', label: '教育' },
  { key: 'entertainment', label: '娱乐' },
  { key: 'professional', label: '专业' }
])

// 计算属性 - 筛选后的角色
const filteredRoles = computed(() => {
  let filtered = aiRoles.value

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(role =>
      role.category === selectedCategory.value ||
      role.name.includes(selectedCategory.value) ||
      role.description.includes(selectedCategory.value)
    )
  }

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(role =>
      role.name.toLowerCase().includes(query) ||
      role.description.toLowerCase().includes(query) ||
      role.personality.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 加载AI角色列表
async function loadAIRoles() {
  isLoading.value = true
  error.value = ''

  try {
    const roles = await aiRoleAPI.getAIRoles()
    // 确保返回的是数组
    aiRoles.value = Array.isArray(roles) ? roles : []
  } catch (err) {
    error.value = err.message || '加载AI角色失败，请稍后重试'
    aiRoles.value = []
  } finally {
    isLoading.value = false
  }
}

// 处理角色卡片点击
function handleRoleClick(role) {
  showRoleDetails(role)
}

// 显示角色详情
function showRoleDetails(role) {
  selectedRoleForDetail.value = role
  detailModalVisible.value = true
}

// 选择AI角色开始聊天
async function selectRole(role) {
  // 检查用户是否已认证
  if (!authStore.isAuthenticated) {
    // 未认证用户，跳转到首页并显示认证模态框
    router.push({
      name: 'home',
      query: {
        showAuth: 'guest',
        redirect: `/chat?roleId=${role.id}`
      }
    })
    return
  }

  try {
    // 创建新的聊天会话
    const session = await chatAPI.createChatSession(role.id)

    // 跳转到聊天页面
    router.push({
      name: 'chat',
      params: { sessionId: session.uuid }
    })
  } catch (err) {
    error.value = err.message || '创建聊天会话失败，请稍后重试'
  }
}

// 从详情模态框开始聊天
function handleStartChatFromDetail(role) {
  detailModalVisible.value = false
  selectRole(role)
}

// 清除搜索
function clearSearch() {
  searchQuery.value = ''
  selectedCategory.value = 'all'
}

// 处理图片加载错误
function handleImageError(event) {
  if (event.target) {
    event.target.style.display = 'none'
    const placeholder = event.target.parentElement?.querySelector('.avatar-placeholder')
    if (placeholder) {
      placeholder.style.display = 'flex'
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAIRoles()
})
</script>

<style scoped>
.ai-roles-view {
  min-height: 100vh;
  padding: var(--space-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-xl);
}

/* 搜索和筛选区域 */
.search-section {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--space-md);
  color: var(--color-text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--space-md) var(--space-md) var(--space-md) 48px;
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  outline: none;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: var(--color-accent-purple);
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.search-input::placeholder {
  color: var(--color-text-muted);
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.filter-tab {
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  padding: var(--space-sm) var(--space-lg);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-purple);
  color: var(--color-text-primary);
}

.filter-tab.active {
  background: var(--color-accent-purple);
  border-color: var(--color-accent-purple);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-glass);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl) 0;
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl) 0;
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--space-3xl) 0;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.empty-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 角色内容区域 */
.roles-content {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.roles-stats {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-xl);
}

.stats-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  padding: var(--space-sm) var(--space-md);
}

.stats-text strong {
  color: var(--color-accent-purple);
  font-weight: var(--font-weight-semibold);
}

/* 没有搜索结果 */
.no-results {
  text-align: center;
  padding: var(--space-3xl);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.no-results-icon {
  color: var(--color-text-muted);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.no-results-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.no-results-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* AI角色列表 */
.roles-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  max-width: 900px;
  margin: 0 auto;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 2px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-lg);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--space-lg);
  position: relative;
  overflow: hidden;
  width: 100%;
}

.role-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-accent-purple), var(--color-accent-purple-light));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.role-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
  border-color: var(--color-accent-purple);
}

.role-card:hover::before {
  opacity: 1;
}

.role-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-lg);
  object-fit: cover;
  border: 2px solid var(--color-border-glass);
  transition: all 0.3s ease;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-border-glass);
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.avatar-glow {
  position: absolute;
  inset: -4px;
  background: linear-gradient(135deg, var(--color-accent-purple), var(--color-accent-purple-light));
  border-radius: var(--border-radius-lg);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  filter: blur(8px);
}

.role-card:hover .avatar-glow {
  opacity: 0.3;
}

.role-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-width: 0; /* 防止flex子元素溢出 */
}

.role-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--space-md);
  justify-content: space-between;
}

.role-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  letter-spacing: -0.02em;
}

.role-badge {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 4px 12px;
  border-radius: var(--border-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.role-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.role-actions {
  display: flex;
  flex-direction: row;
  gap: var(--space-sm);
  flex-shrink: 0;
}

.btn-start-chat {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  white-space: nowrap;
}

.btn-start-chat:hover {
  background: var(--color-accent-yellow-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-glass);
}

.btn-start-chat:active {
  transform: scale(0.98);
}

.btn-view-details {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-glass);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-view-details:hover {
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
  border-color: var(--color-accent-purple);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .roles-list {
    max-width: 700px;
  }
}

@media (max-width: 768px) {
  .ai-roles-view {
    padding: var(--space-lg) 0;
  }

  .container {
    padding: 0 var(--space-md);
  }

  .page-header {
    margin-bottom: var(--space-xl);
  }

  .page-title {
    font-size: 2rem;
  }

  .page-description {
    font-size: var(--font-size-base);
  }

  .search-section {
    max-width: none;
  }

  .filter-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--space-xs);
  }

  .filter-tab {
    white-space: nowrap;
    flex-shrink: 0;
  }

  .roles-list {
    max-width: none;
    gap: var(--space-md);
  }

  .role-card {
    padding: var(--space-md);
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .role-avatar {
    width: 60px;
    height: 60px;
    align-self: center;
  }

  .role-header {
    flex-direction: column;
    gap: var(--space-sm);
    text-align: center;
  }

  .role-actions {
    flex-direction: row;
    gap: var(--space-sm);
    justify-content: center;
  }

  .btn-start-chat,
  .btn-view-details {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-sm);
  }

  .page-title {
    font-size: 1.75rem;
  }

  .search-input {
    padding: var(--space-sm) var(--space-sm) var(--space-sm) 40px;
  }

  .search-icon {
    left: var(--space-sm);
  }

  .filter-tab {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
  }

  .roles-list {
    gap: var(--space-sm);
  }

  .role-card {
    padding: var(--space-sm);
  }

  .role-avatar {
    width: 50px;
    height: 50px;
  }

  .role-actions {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .btn-start-chat,
  .btn-view-details {
    width: 100%;
    padding: var(--space-xs) var(--space-md);
  }
}
</style>
