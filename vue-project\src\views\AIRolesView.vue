<template>
  <div class="ai-roles-view">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">选择你的AI伙伴</h1>
        <p class="page-description">
          每个AI伙伴都有独特的性格和专长，选择最适合你当前心情的那一个开始对话吧。
        </p>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载AI角色...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ error }}</p>
        <button class="btn btn-primary" @click="loadAIRoles">
          重试
        </button>
      </div>

      <!-- AI角色列表 -->
      <div v-else class="roles-grid">
        <div 
          v-for="role in aiRoles" 
          :key="role.id"
          class="role-card"
          @click="selectRole(role)"
        >
          <div class="role-avatar">
            <img 
              v-if="role.avatar_url" 
              :src="role.avatar_url" 
              :alt="role.name"
              class="avatar-image"
            />
            <div v-else class="avatar-placeholder">
              <span class="avatar-initial">{{ role.name.charAt(0) }}</span>
            </div>
          </div>
          
          <div class="role-info">
            <h3 class="role-name">{{ role.name }}</h3>
            <p class="role-description">{{ role.description }}</p>
          </div>
          
          <div class="role-action">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { aiRoleAPI, chatAPI } from '@/services/api'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const aiRoles = ref([])
const isLoading = ref(false)
const error = ref('')

// 加载AI角色列表
async function loadAIRoles() {
  isLoading.value = true
  error.value = ''

  try {
    const roles = await aiRoleAPI.getAIRoles()
    aiRoles.value = roles
  } catch (err) {
    error.value = err.message || '加载AI角色失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 选择AI角色
async function selectRole(role) {
  // 检查用户是否已认证
  if (!authStore.isAuthenticated) {
    // 未认证用户，跳转到首页并显示认证模态框
    router.push({
      name: 'home',
      query: {
        showAuth: 'guest',
        redirect: `/chat?roleId=${role.id}`
      }
    })
    return
  }

  try {
    // 创建新的聊天会话
    const session = await chatAPI.createChatSession(role.id)
    
    // 跳转到聊天页面
    router.push({
      name: 'chat',
      params: { sessionId: session.uuid }
    })
  } catch (err) {
    error.value = err.message || '创建聊天会话失败，请稍后重试'
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAIRoles()
})
</script>

<style scoped>
.ai-roles-view {
  min-height: 100vh;
  padding: var(--space-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-normal);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl) 0;
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl) 0;
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* AI角色网格 */
.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-lg);
}

.role-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.role-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
  border-color: var(--color-accent-purple);
}

.role-avatar {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-md);
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-initial {
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.role-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.role-action {
  flex-shrink: 0;
  color: var(--color-text-muted);
  transition: color 0.2s ease;
}

.role-card:hover .role-action {
  color: var(--color-accent-purple);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .roles-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .role-card {
    padding: var(--space-md);
  }
  
  .role-avatar {
    width: 50px;
    height: 50px;
  }
}
</style>
