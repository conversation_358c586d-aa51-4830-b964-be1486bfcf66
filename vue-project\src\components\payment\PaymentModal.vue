<template>
  <Teleport to="body">
    <div v-if="paymentStore.showPaymentModal" class="payment-modal-overlay" @click="handleOverlayClick">
      <div class="payment-modal" @click.stop>
        <!-- 模态框头部 -->
        <div class="modal-header">
          <h3 class="modal-title">支付订单</h3>
          <button class="close-button" @click="paymentStore.closePaymentModal()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- 订单信息 -->
        <div v-if="paymentStore.currentOrder" class="order-info">
          <div class="order-item">
            <span class="order-label">订单号</span>
            <span class="order-value">{{ formatOrderId(paymentStore.currentOrder.order_uuid) }}</span>
          </div>
          <div class="order-item">
            <span class="order-label">套餐名称</span>
            <span class="order-value">{{ paymentStore.selectedPlan?.name }}</span>
          </div>
          <div class="order-item">
            <span class="order-label">支付金额</span>
            <span class="order-value price">{{ paymentStore.formatPrice(paymentStore.currentOrder.amount) }}</span>
          </div>
          <div class="order-item">
            <span class="order-label">过期时间</span>
            <span class="order-value">{{ formatExpireTime(paymentStore.currentOrder.payment_details?.expire_time) }}</span>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-methods">
          <h4 class="methods-title">选择支付方式</h4>
          
          <!-- 模拟支付链接 -->
          <button 
            class="payment-method-btn primary"
            @click="openPaymentUrl"
            :disabled="!paymentStore.currentOrder?.payment_details?.pay_url"
          >
            <div class="method-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="method-content">
              <span class="method-name">在线支付</span>
              <span class="method-desc">跳转到支付页面完成支付</span>
            </div>
          </button>

          <!-- 测试用的模拟支付按钮 -->
          <div class="test-section">
            <p class="test-title">测试功能（仅开发环境）</p>
            <div class="test-buttons">
              <button 
                class="payment-method-btn success"
                @click="handleSimulatePayment('success')"
                :disabled="paymentStore.isLoading"
              >
                <div class="method-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="method-content">
                  <span class="method-name">模拟支付成功</span>
                  <span class="method-desc">直接完成支付流程</span>
                </div>
              </button>

              <button 
                class="payment-method-btn danger"
                @click="handleSimulatePayment('failed')"
                :disabled="paymentStore.isLoading"
              >
                <div class="method-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="method-content">
                  <span class="method-name">模拟支付失败</span>
                  <span class="method-desc">测试支付失败场景</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="paymentStore.isLoading" class="loading-overlay">
          <div class="loading-content">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <p>正在处理支付...</p>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { usePaymentStore } from '@/stores/payment'

// 状态管理
const paymentStore = usePaymentStore()

// 处理覆盖层点击
function handleOverlayClick() {
  if (!paymentStore.isLoading) {
    paymentStore.closePaymentModal()
  }
}

// 打开支付链接
function openPaymentUrl() {
  const payUrl = paymentStore.currentOrder?.payment_details?.pay_url
  if (payUrl) {
    window.open(payUrl, '_blank')
  }
}

// 处理模拟支付
async function handleSimulatePayment(status) {
  const success = await paymentStore.simulatePayment(status)
  
  if (success) {
    // 显示成功提示
    if (status === 'success') {
      // 可以添加成功提示逻辑
      console.log('支付成功！')
    } else {
      console.log('支付失败！')
    }
  }
}

// 格式化订单ID
function formatOrderId(uuid) {
  if (!uuid) return ''
  return uuid.substring(0, 8).toUpperCase()
}

// 格式化过期时间
function formatExpireTime(expireTime) {
  if (!expireTime) return '未知'
  
  const date = new Date(expireTime)
  const now = new Date()
  const diff = date - now
  
  if (diff <= 0) {
    return '已过期'
  }
  
  const minutes = Math.floor(diff / 60000)
  if (minutes < 60) {
    return `${minutes}分钟后过期`
  }
  
  const hours = Math.floor(minutes / 60)
  return `${hours}小时${minutes % 60}分钟后过期`
}
</script>

<style scoped>
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--space-lg);
  animation: fadeIn 0.3s ease;
}

.payment-modal {
  position: relative;
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl);
  border-bottom: 1px solid var(--color-border-glass);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--color-text-secondary);
  background: var(--color-surface-secondary);
}

/* 订单信息 */
.order-info {
  padding: var(--space-xl);
  border-bottom: 1px solid var(--color-border-glass);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.order-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.order-value.price {
  font-size: var(--font-size-lg);
  color: var(--color-accent-yellow);
  font-weight: var(--font-weight-semibold);
}

/* 支付方式 */
.payment-methods {
  padding: var(--space-xl);
}

.methods-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
}

.payment-method-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--space-md);
}

.payment-method-btn:hover:not(:disabled) {
  background: var(--color-surface-primary);
  border-color: var(--color-accent-purple);
  transform: translateY(-1px);
}

.payment-method-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.payment-method-btn.primary {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  border-color: var(--color-accent-yellow);
}

.payment-method-btn.primary:hover:not(:disabled) {
  background: var(--color-accent-yellow);
  box-shadow: var(--shadow-glow-yellow);
}

.payment-method-btn.success {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
  color: #4CAF50;
}

.payment-method-btn.danger {
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--color-error);
  color: var(--color-error);
}

.method-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
}

.method-content {
  flex: 1;
  text-align: left;
}

.method-name {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-xs);
}

.method-desc {
  display: block;
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

/* 测试区块 */
.test-section {
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border-glass);
}

.test-title {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  text-align: center;
  margin-bottom: var(--space-lg);
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-xl);
}

.loading-content {
  text-align: center;
  color: var(--color-text-primary);
}

.loading-content p {
  margin-top: var(--space-md);
  font-size: var(--font-size-sm);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-modal {
    margin: var(--space-md);
    max-width: none;
  }

  .modal-header,
  .order-info,
  .payment-methods {
    padding: var(--space-lg);
  }

  .test-buttons {
    gap: var(--space-xs);
  }
}
</style>
