<template>
  <div class="chat-history-card">
    <div class="card-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8V12L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="card-title">聊天历史</h2>
          <p class="card-subtitle">查看您的对话记录</p>
        </div>
        <div class="header-actions">
          <button 
            class="btn btn-secondary btn-sm"
            @click="userStore.loadChatSessions()"
            :disabled="userStore.isLoading"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 4V10H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 15C19.9828 16.6224 19.0209 18.0668 17.7422 19.1573C16.4635 20.2477 14.9274 20.9385 13.3 21.15C11.6726 21.3615 10.0284 21.0906 8.56822 20.3712C7.10803 19.6517 5.90 18.5168 5.09 17.1C4.28 15.6832 3.90578 14.0479 4.01 12.4C4.11422 10.7521 4.69234 9.17254 5.68 7.84C6.66766 6.50746 8.01834 5.47776 9.57 4.86C11.1217 4.24224 12.8248 4.06768 14.49 4.36" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <div class="card-content">
      <!-- 加载状态 -->
      <div v-if="userStore.isLoading && !userStore.hasChatSessions" class="loading-content">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <p>正在加载聊天历史...</p>
      </div>

      <!-- 聊天会话列表 -->
      <div v-else-if="userStore.hasChatSessions" class="sessions-list">
        <div
          v-for="session in userStore.chatSessions"
          :key="session.uuid"
          class="session-item"
          @click="goToChat(session.uuid)"
        >
          <div class="session-info">
            <div class="session-header">
              <h3 class="session-title">{{ session.title }}</h3>
              <span class="session-time">{{ formatTime(session.updated_at) }}</span>
            </div>
            <div class="session-details">
              <span class="session-role">{{ session.ai_role_name }}</span>
              <span class="session-messages">{{ session.message_count || 0 }} 条消息</span>
            </div>
          </div>
          <div class="session-action">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>

        <!-- 分页或加载更多 -->
        <div v-if="userStore.chatSessions.length >= 10" class="load-more">
          <button class="btn btn-secondary" @click="loadMoreSessions">
            加载更多
          </button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-content">
        <div class="empty-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="empty-title">还没有聊天记录</h3>
        <p class="empty-description">开始与AI伙伴的对话，创建您的第一个聊天记录</p>
        <router-link to="/ai-roles" class="btn btn-primary">
          选择AI角色
        </router-link>
      </div>

      <!-- 错误状态 -->
      <div v-if="userStore.error && !userStore.hasChatSessions" class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ userStore.error }}</p>
        <button class="btn btn-primary" @click="userStore.loadChatSessions()">
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }
  
  // 小于24小时
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }
  
  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`
  }
  
  // 显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 跳转到聊天页面
function goToChat(sessionUuid) {
  router.push({
    name: 'chat',
    params: { sessionId: sessionUuid }
  })
}

// 加载更多会话
function loadMoreSessions() {
  // 这里可以实现分页加载逻辑
  console.log('加载更多会话')
}
</script>

<style scoped>
.chat-history-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chat-history-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

/* 卡片头部 */
.card-header {
  background: var(--color-surface-primary);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

/* 卡片内容 */
.card-content {
  padding: var(--space-lg);
}

/* 会话列表 */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.session-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--color-surface-secondary);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  background: var(--color-surface-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-glass);
  border-color: var(--color-accent-purple);
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-xs);
  gap: var(--space-md);
}

.session-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.session-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  white-space: nowrap;
  flex-shrink: 0;
}

.session-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-md);
}

.session-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
}

.session-messages {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.session-action {
  flex-shrink: 0;
  color: var(--color-text-muted);
  transition: color 0.2s ease;
}

.session-item:hover .session-action {
  color: var(--color-accent-purple);
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border-glass);
}

/* 空状态和错误状态 */
.empty-content, .error-content, .loading-content {
  text-align: center;
  padding: var(--space-3xl);
}

.empty-icon, .error-icon {
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.empty-icon {
  color: var(--color-text-muted);
}

.error-icon {
  color: var(--color-error);
}

.empty-title, .error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.empty-description, .error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-normal);
}

.loading-content p {
  color: var(--color-text-secondary);
  margin-top: var(--space-md);
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .session-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .session-time {
    align-self: flex-end;
  }

  .session-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .header-actions {
    display: none;
  }
}
</style>
