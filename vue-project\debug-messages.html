<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息重复问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        .warning {
            border-left: 4px solid #FF9800;
        }
        input {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        .message-item {
            background: #444;
            padding: 8px;
            margin: 3px 0;
            border-radius: 4px;
            border-left: 4px solid #666;
        }
        .duplicate {
            border-left-color: #F44336;
            background: #4a2c2c;
        }
        .user-message {
            border-left-color: #9B59B6;
        }
        .assistant-message {
            border-left-color: #2E7D32;
        }
    </style>
</head>
<body>
    <h1>消息重复问题调试工具</h1>
    
    <div class="test-section">
        <h2>1. 用户认证</h2>
        <input type="email" id="auth-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="auth-password" placeholder="密码" value="password123">
        <button onclick="testLogin()">登录获取Token</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 获取会话列表</h2>
        <button onclick="testGetSessions()">获取会话列表</button>
        <div id="sessions-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 检查消息重复</h2>
        <input type="text" id="session-uuid" placeholder="会话UUID">
        <button onclick="checkMessageDuplicates()">检查消息重复</button>
        <div id="duplicate-result" class="result"></div>
        <div id="messages-display"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';

        function displayResult(elementId, data, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${type}`;
        }

        async function testLogin() {
            const email = document.getElementById('auth-email').value;
            const password = document.getElementById('auth-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = data.data.token;
                    displayResult('auth-result', data, 'success');
                } else {
                    displayResult('auth-result', data, 'error');
                }
            } catch (error) {
                displayResult('auth-result', { error: error.message }, 'error');
            }
        }

        async function testGetSessions() {
            if (!currentToken) {
                displayResult('sessions-result', { error: '请先登录获取token' }, 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('sessions-result', data, 'success');
                    
                    // 自动填入第一个会话的UUID
                    if (data.data && data.data.length > 0) {
                        document.getElementById('session-uuid').value = data.data[0].uuid;
                    }
                } else {
                    displayResult('sessions-result', data, 'error');
                }
            } catch (error) {
                displayResult('sessions-result', { error: error.message }, 'error');
            }
        }

        async function checkMessageDuplicates() {
            const sessionUuid = document.getElementById('session-uuid').value;
            
            if (!currentToken) {
                displayResult('duplicate-result', { error: '请先登录获取token' }, 'error');
                return;
            }
            
            if (!sessionUuid) {
                displayResult('duplicate-result', { error: '请输入会话UUID' }, 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${sessionUuid}/messages`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    const messages = data.data;
                    const analysis = analyzeMessageDuplicates(messages);
                    
                    displayResult('duplicate-result', analysis, analysis.hasDuplicates ? 'warning' : 'success');
                    displayMessages(messages, analysis.duplicateGroups);
                } else {
                    displayResult('duplicate-result', data, 'error');
                }
            } catch (error) {
                displayResult('duplicate-result', { error: error.message }, 'error');
            }
        }

        function analyzeMessageDuplicates(messages) {
            const messageGroups = new Map();
            const duplicateGroups = [];
            
            // 按内容分组
            messages.forEach((message, index) => {
                const key = `${message.role}-${message.content.trim()}`;
                
                if (!messageGroups.has(key)) {
                    messageGroups.set(key, []);
                }
                messageGroups.get(key).push({ ...message, originalIndex: index });
            });
            
            // 找出重复的组
            messageGroups.forEach((group, key) => {
                if (group.length > 1) {
                    duplicateGroups.push({
                        key,
                        count: group.length,
                        messages: group
                    });
                }
            });
            
            return {
                totalMessages: messages.length,
                uniqueMessages: messageGroups.size,
                hasDuplicates: duplicateGroups.length > 0,
                duplicateGroups,
                duplicateCount: duplicateGroups.reduce((sum, group) => sum + group.count - 1, 0)
            };
        }

        function displayMessages(messages, duplicateGroups) {
            const container = document.getElementById('messages-display');
            
            if (!messages || messages.length === 0) {
                container.innerHTML = '<p>没有找到消息</p>';
                return;
            }

            // 创建重复消息的索引映射
            const duplicateIndexes = new Set();
            duplicateGroups.forEach(group => {
                group.messages.forEach(msg => {
                    duplicateIndexes.add(msg.originalIndex);
                });
            });

            container.innerHTML = messages.map((message, index) => {
                const isDuplicate = duplicateIndexes.has(index);
                const cssClass = `message-item ${message.role}-message ${isDuplicate ? 'duplicate' : ''}`;
                
                return `
                    <div class="${cssClass}">
                        <strong>${message.role} ${isDuplicate ? '(重复)' : ''}:</strong><br>
                        ${message.content}<br>
                        <small>时间: ${new Date(message.created_at).toLocaleString()}</small>
                    </div>
                `;
            }).join('');
        }

        // 页面加载时自动尝试登录
        window.onload = function() {
            // 可以在这里添加自动登录逻辑
        };
    </script>
</body>
</html>
