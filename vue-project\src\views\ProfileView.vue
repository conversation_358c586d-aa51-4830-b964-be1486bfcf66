<template>
  <div class="profile-view">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
        <p class="page-description">管理您的账户信息和聊天历史</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="userStore.isLoading && !userStore.hasUserInfo" class="loading-state">
        <div class="loading-spinner">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <p class="loading-text">正在加载用户信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="userStore.error && !userStore.hasUserInfo" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">加载失败</h3>
        <p class="error-message">{{ userStore.error }}</p>
        <button class="btn btn-primary" @click="userStore.refreshData()">
          重试
        </button>
      </div>

      <!-- 主要内容 -->
      <div v-else class="profile-content">
        <!-- 用户信息卡片 -->
        <UserInfoCard />

        <!-- 密码修改卡片 -->
        <PasswordChangeCard v-if="!userStore.isGuest" />

        <!-- 聊天历史卡片 -->
        <ChatHistoryCard />
      </div>

      <!-- 全局错误提示 -->
      <div v-if="userStore.error" class="error-toast">
        {{ userStore.error }}
        <button @click="userStore.clearError()" class="error-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter'
import { useUserStore } from '@/stores/user'
import UserInfoCard from '@/components/profile/UserInfoCard.vue'
import PasswordChangeCard from '@/components/profile/PasswordChangeCard.vue'
import ChatHistoryCard from '@/components/profile/ChatHistoryCard.vue'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户认证状态
  if (!authStore.isAuthenticated) {
    router.push({
      name: 'home',
      query: { showAuth: 'guest' }
    })
    return
  }

  // 加载用户数据
  await userStore.refreshData()
})
</script>

<style scoped>
.profile-view {
  min-height: 100vh;
  background: var(--color-background-dark);
  padding: var(--space-xl) 0;
}

.container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  letter-spacing: -0.02em;
}

.page-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: var(--space-3xl);
}

.loading-spinner {
  margin-bottom: var(--space-lg);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: var(--space-3xl);
}

.error-icon {
  color: var(--color-error);
  margin-bottom: var(--space-lg);
  display: flex;
  justify-content: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

/* 主要内容 */
.profile-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-glass);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  z-index: var(--z-index-modal);
  animation: slideInUp 0.3s ease;
}

.error-close {
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-view {
    padding: var(--space-lg) 0;
  }

  .container {
    padding: 0 var(--space-md);
  }

  .page-header {
    margin-bottom: var(--space-xl);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .profile-content {
    gap: var(--space-lg);
  }
}
</style>
