<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块五 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        button.success {
            background: #4CAF50;
        }
        button.danger {
            background: #F44336;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        input, select {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .plan-item {
            background: #444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #9B59B6;
        }
        .order-item {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #FFD60A;
        }
        .subscription-item {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>模块五 API 测试 - 支付与充值</h1>
    
    <div class="test-section">
        <h2>1. 用户认证</h2>
        <input type="email" id="auth-email" placeholder="邮箱" value="<EMAIL>">
        <input type="password" id="auth-password" placeholder="密码" value="newpassword123">
        <button onclick="testLogin()">登录获取Token</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="grid">
        <div class="test-section">
            <h2>2. 获取套餐列表</h2>
            <button onclick="testGetPlans()">获取所有套餐</button>
            <div id="plans-result" class="result"></div>
            <div id="plans-display"></div>
        </div>

        <div class="test-section">
            <h2>3. 创建订单</h2>
            <select id="plan-select">
                <option value="">请选择套餐</option>
            </select>
            <button onclick="testCreateOrder()">创建订单</button>
            <div id="order-result" class="result"></div>
            <div id="current-order-display"></div>
        </div>
    </div>

    <div class="grid">
        <div class="test-section">
            <h2>4. 模拟支付</h2>
            <input type="text" id="order-uuid" placeholder="订单UUID">
            <button class="success" onclick="testSimulatePayment('success')">模拟支付成功</button>
            <button class="danger" onclick="testSimulatePayment('failed')">模拟支付失败</button>
            <div id="payment-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>5. 获取用户订阅</h2>
            <button onclick="testGetSubscriptions()">获取订阅信息</button>
            <div id="subscriptions-result" class="result"></div>
            <div id="subscriptions-display"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>6. 获取订单历史</h2>
        <button onclick="testGetOrders()">获取订单历史</button>
        <div id="orders-result" class="result"></div>
        <div id="orders-display"></div>
    </div>

    <div class="test-section">
        <h2>7. 边界条件测试</h2>
        <button onclick="testInvalidPlan()">测试无效套餐ID</button>
        <button onclick="testUnauthorizedAccess()">测试未授权访问</button>
        <button onclick="testInvalidOrder()">测试无效订单UUID</button>
        <div id="boundary-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';
        let availablePlans = [];

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        function displayPlans(plans) {
            const container = document.getElementById('plans-display');
            const select = document.getElementById('plan-select');
            
            if (!plans || plans.length === 0) {
                container.innerHTML = '<p>没有找到套餐</p>';
                return;
            }

            // 更新选择框
            select.innerHTML = '<option value="">请选择套餐</option>';
            plans.forEach(plan => {
                const option = document.createElement('option');
                option.value = plan.id;
                option.textContent = `${plan.name} - ¥${plan.price}`;
                select.appendChild(option);
            });

            // 显示套餐列表
            container.innerHTML = plans.map(plan => `
                <div class="plan-item">
                    <strong>${plan.name}</strong> (${plan.plan_type})<br>
                    价格: ¥${plan.price}<br>
                    描述: ${plan.description}<br>
                    ${plan.plan_type === 'subscription' ? `有效期: ${plan.duration_days}天` : `消息数: ${plan.message_credits}`}
                </div>
            `).join('');
        }

        function displayCurrentOrder(order) {
            const container = document.getElementById('current-order-display');
            if (!order) {
                container.innerHTML = '';
                return;
            }

            document.getElementById('order-uuid').value = order.order_uuid;
            
            container.innerHTML = `
                <div class="order-item">
                    <strong>订单创建成功</strong><br>
                    订单号: ${order.order_uuid}<br>
                    金额: ¥${order.amount}<br>
                    状态: ${order.status}<br>
                    ${order.payment_details ? `支付链接: ${order.payment_details.pay_url}` : ''}
                </div>
            `;
        }

        function displaySubscriptions(subscriptions) {
            const container = document.getElementById('subscriptions-display');
            if (!subscriptions || subscriptions.length === 0) {
                container.innerHTML = '<p>没有找到订阅记录</p>';
                return;
            }

            container.innerHTML = subscriptions.map(sub => `
                <div class="subscription-item">
                    <strong>${sub.plan_name}</strong><br>
                    状态: ${sub.status}<br>
                    开始时间: ${new Date(sub.start_date).toLocaleString()}<br>
                    结束时间: ${new Date(sub.end_date).toLocaleString()}
                </div>
            `).join('');
        }

        function displayOrders(orders) {
            const container = document.getElementById('orders-display');
            if (!orders || orders.length === 0) {
                container.innerHTML = '<p>没有找到订单记录</p>';
                return;
            }

            container.innerHTML = orders.map(order => `
                <div class="order-item">
                    <strong>${order.plan_name}</strong><br>
                    订单号: ${order.order_uuid.substring(0, 8).toUpperCase()}<br>
                    金额: ¥${order.amount}<br>
                    状态: ${order.status}<br>
                    创建时间: ${new Date(order.created_at).toLocaleString()}
                    ${order.completed_at ? `<br>完成时间: ${new Date(order.completed_at).toLocaleString()}` : ''}
                </div>
            `).join('');
        }

        async function testLogin() {
            const email = document.getElementById('auth-email').value;
            const password = document.getElementById('auth-password').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentToken = data.data.token;
                    displayResult('auth-result', data);
                } else {
                    displayResult('auth-result', data, true);
                }
            } catch (error) {
                displayResult('auth-result', { error: error.message }, true);
            }
        }

        async function testGetPlans() {
            try {
                const response = await fetch(`${API_BASE_URL}/plans`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    availablePlans = data.data;
                    displayResult('plans-result', data);
                    displayPlans(data.data);
                } else {
                    displayResult('plans-result', data, true);
                }
            } catch (error) {
                displayResult('plans-result', { error: error.message }, true);
            }
        }

        async function testCreateOrder() {
            const planId = document.getElementById('plan-select').value;
            
            if (!planId) {
                displayResult('order-result', { error: '请选择套餐' }, true);
                return;
            }

            if (!currentToken) {
                displayResult('order-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/orders`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ plan_id: parseInt(planId) })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('order-result', data);
                    displayCurrentOrder(data.data);
                } else {
                    displayResult('order-result', data, true);
                }
            } catch (error) {
                displayResult('order-result', { error: error.message }, true);
            }
        }

        async function testSimulatePayment(status) {
            const orderUuid = document.getElementById('order-uuid').value;
            
            if (!orderUuid) {
                displayResult('payment-result', { error: '请输入订单UUID' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/payments/notify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        order_uuid: orderUuid,
                        status: status,
                        transaction_id: 'mock_txn_' + Date.now(),
                        gateway: 'mock_payment'
                    })
                });
                
                const result = await response.text();
                displayResult('payment-result', { status: status, result: result }, result !== 'SUCCESS');
            } catch (error) {
                displayResult('payment-result', { error: error.message }, true);
            }
        }

        async function testGetSubscriptions() {
            if (!currentToken) {
                displayResult('subscriptions-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/subscriptions`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('subscriptions-result', data);
                    displaySubscriptions(data.data);
                } else {
                    displayResult('subscriptions-result', data, true);
                }
            } catch (error) {
                displayResult('subscriptions-result', { error: error.message }, true);
            }
        }

        async function testGetOrders() {
            if (!currentToken) {
                displayResult('orders-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/me/orders`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    displayResult('orders-result', data);
                    displayOrders(data.data);
                } else {
                    displayResult('orders-result', data, true);
                }
            } catch (error) {
                displayResult('orders-result', { error: error.message }, true);
            }
        }

        async function testInvalidPlan() {
            if (!currentToken) {
                displayResult('boundary-result', { error: '请先登录获取token' }, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/orders`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ plan_id: 99999 })
                });
                
                const data = await response.json();
                displayResult('boundary-result', { test: '无效套餐ID', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: '无效套餐ID', error: error.message }, true);
            }
        }

        async function testUnauthorizedAccess() {
            try {
                const response = await fetch(`${API_BASE_URL}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ plan_id: 1 })
                });
                
                const data = await response.json();
                displayResult('boundary-result', { test: '未授权访问', response: data }, !response.ok);
            } catch (error) {
                displayResult('boundary-result', { test: '未授权访问', error: error.message }, true);
            }
        }

        async function testInvalidOrder() {
            try {
                const response = await fetch(`${API_BASE_URL}/payments/notify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        order_uuid: 'invalid-uuid',
                        status: 'success',
                        transaction_id: 'test_txn',
                        gateway: 'test'
                    })
                });
                
                const result = await response.text();
                displayResult('boundary-result', { test: '无效订单UUID', result: result }, result !== 'SUCCESS');
            } catch (error) {
                displayResult('boundary-result', { test: '无效订单UUID', error: error.message }, true);
            }
        }
    </script>
</body>
</html>
