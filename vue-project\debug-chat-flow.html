<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天流程完整调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        button {
            background: #9B59B6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8E44AD;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #F44336;
        }
        .warning {
            border-left: 4px solid #FF9800;
        }
        input, textarea {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        .step {
            background: #1e3a8a;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-weight: bold;
        }
        .summary {
            background: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary.error {
            background: #7f1d1d;
        }
        .message-item {
            background: #444;
            padding: 8px;
            margin: 3px 0;
            border-radius: 4px;
            border-left: 4px solid #666;
        }
        .duplicate {
            border-left-color: #F44336;
            background: #4a2c2c;
        }
        .user-message {
            border-left-color: #9B59B6;
        }
        .assistant-message {
            border-left-color: #2E7D32;
        }
        .flow-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .flow-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🔍 聊天流程完整调试工具</h1>
    <p>这个工具将注册新账号，创建会话，发送消息，然后检查是否有重复问题</p>
    
    <div class="flow-container">
        <div>
            <div class="test-section">
                <h2>📝 步骤1: 注册新账号</h2>
                <input type="email" id="new-email" placeholder="新邮箱">
                <input type="password" id="new-password" placeholder="密码" value="password123">
                <br>
                <button onclick="generateRandomEmail()">生成随机邮箱</button>
                <button onclick="registerNewUser()" id="register-btn">注册新用户</button>
                <div id="register-result" class="result"></div>
            </div>

            <div class="test-section">
                <h2>🤖 步骤2: 创建聊天会话</h2>
                <select id="ai-role-select">
                    <option value="1">博学的历史学家</option>
                    <option value="2">温柔的心理顾问</option>
                    <option value="3">活泼的生活助手</option>
                    <option value="4">理性的分析师</option>
                </select>
                <button onclick="createChatSession()" id="create-session-btn" disabled>创建会话</button>
                <div id="session-result" class="result"></div>
            </div>

            <div class="test-section">
                <h2>💬 步骤3: 发送测试消息</h2>
                <textarea id="test-message" placeholder="测试消息" rows="3">你好，这是一条测试消息</textarea>
                <br>
                <button onclick="sendTestMessage()" id="send-message-btn" disabled>发送消息</button>
                <button onclick="sendMultipleMessages()" id="send-multiple-btn" disabled>发送3条相同消息(测试重复)</button>
                <div id="message-result" class="result"></div>
            </div>
        </div>

        <div>
            <div class="test-section">
                <h2>🔍 步骤4: 检查消息重复</h2>
                <button onclick="checkForDuplicates()" id="check-duplicates-btn" disabled>检查重复消息</button>
                <div id="duplicate-analysis" class="result"></div>
            </div>

            <div class="test-section">
                <h2>📊 完整消息列表</h2>
                <div id="messages-display"></div>
            </div>

            <div class="test-section">
                <h2>📋 测试总结</h2>
                <div id="test-summary" class="summary">
                    等待测试完成...
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        let currentToken = '';
        let currentSessionUuid = '';
        let testResults = {
            register: false,
            createSession: false,
            sendMessage: false,
            hasDuplicates: false,
            totalMessages: 0,
            uniqueMessages: 0
        };

        function displayResult(elementId, data, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${type}`;
        }

        function updateSummary() {
            const summary = document.getElementById('test-summary');
            const isSuccess = testResults.register && testResults.createSession && testResults.sendMessage;
            
            let summaryText = `🔍 测试结果总结:\n\n`;
            summaryText += `✅ 注册账号: ${testResults.register ? '成功' : '失败'}\n`;
            summaryText += `✅ 创建会话: ${testResults.createSession ? '成功' : '失败'}\n`;
            summaryText += `✅ 发送消息: ${testResults.sendMessage ? '成功' : '失败'}\n`;
            
            if (testResults.totalMessages > 0) {
                summaryText += `\n📊 消息统计:\n`;
                summaryText += `- 总消息数: ${testResults.totalMessages}\n`;
                summaryText += `- 唯一消息数: ${testResults.uniqueMessages}\n`;
                summaryText += `- 是否有重复: ${testResults.hasDuplicates ? '是 ❌' : '否 ✅'}\n`;
                
                if (testResults.hasDuplicates) {
                    summaryText += `\n🚨 发现重复消息！这是后端问题！\n`;
                    summaryText += `建议检查后端消息存储逻辑。\n`;
                } else {
                    summaryText += `\n✅ 没有发现重复消息，后端工作正常。\n`;
                }
            }
            
            summary.textContent = summaryText;
            summary.className = `summary ${testResults.hasDuplicates ? 'error' : ''}`;
        }

        function generateRandomEmail() {
            const timestamp = Date.now();
            const randomStr = Math.random().toString(36).substring(2, 8);
            const email = `test_${timestamp}_${randomStr}@example.com`;
            document.getElementById('new-email').value = email;
        }

        async function registerNewUser() {
            const email = document.getElementById('new-email').value;
            const password = document.getElementById('new-password').value;
            
            if (!email || !password) {
                displayResult('register-result', { error: '请填写邮箱和密码' }, 'error');
                return;
            }
            
            document.getElementById('register-btn').disabled = true;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && (data.code === 200 || data.code === 201)) {
                    currentToken = data.data.token;
                    testResults.register = true;
                    displayResult('register-result', data, 'success');
                    document.getElementById('create-session-btn').disabled = false;
                } else {
                    testResults.register = false;
                    displayResult('register-result', data, 'error');
                }
            } catch (error) {
                testResults.register = false;
                displayResult('register-result', { error: error.message }, 'error');
            } finally {
                document.getElementById('register-btn').disabled = false;
                updateSummary();
            }
        }

        async function createChatSession() {
            const roleId = document.getElementById('ai-role-select').value;
            
            if (!currentToken) {
                displayResult('session-result', { error: '请先注册账号' }, 'error');
                return;
            }
            
            document.getElementById('create-session-btn').disabled = true;
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ai_role_id: parseInt(roleId) })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 201) {
                    currentSessionUuid = data.data.uuid;
                    testResults.createSession = true;
                    displayResult('session-result', data, 'success');
                    document.getElementById('send-message-btn').disabled = false;
                    document.getElementById('send-multiple-btn').disabled = false;
                    document.getElementById('check-duplicates-btn').disabled = false;
                } else {
                    testResults.createSession = false;
                    displayResult('session-result', data, 'error');
                }
            } catch (error) {
                testResults.createSession = false;
                displayResult('session-result', { error: error.message }, 'error');
            } finally {
                document.getElementById('create-session-btn').disabled = false;
                updateSummary();
            }
        }

        async function sendTestMessage() {
            const content = document.getElementById('test-message').value;
            
            if (!currentToken || !currentSessionUuid) {
                displayResult('message-result', { error: '请先创建会话' }, 'error');
                return;
            }
            
            document.getElementById('send-message-btn').disabled = true;
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${currentSessionUuid}/messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content })
                });
                
                const data = await response.json();
                
                if (response.ok && (data.code === 200 || data.code === 201)) {
                    testResults.sendMessage = true;
                    displayResult('message-result', data, 'success');
                } else {
                    testResults.sendMessage = false;
                    displayResult('message-result', data, 'error');
                }
            } catch (error) {
                testResults.sendMessage = false;
                displayResult('message-result', { error: error.message }, 'error');
            } finally {
                document.getElementById('send-message-btn').disabled = false;
                updateSummary();
            }
        }

        async function sendMultipleMessages() {
            const content = document.getElementById('test-message').value;
            
            if (!currentToken || !currentSessionUuid) {
                displayResult('message-result', { error: '请先创建会话' }, 'error');
                return;
            }
            
            document.getElementById('send-multiple-btn').disabled = true;
            
            let results = [];
            
            for (let i = 0; i < 3; i++) {
                try {
                    const response = await fetch(`${API_BASE_URL}/chat/sessions/${currentSessionUuid}/messages`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${currentToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ content: `${content} (第${i+1}次)` })
                    });
                    
                    const data = await response.json();
                    results.push({ attempt: i+1, success: response.ok, data });
                    
                    // 稍微延迟，避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 500));
                } catch (error) {
                    results.push({ attempt: i+1, success: false, error: error.message });
                }
            }
            
            displayResult('message-result', { multipleMessages: results }, 'warning');
            document.getElementById('send-multiple-btn').disabled = false;
        }

        async function checkForDuplicates() {
            if (!currentToken || !currentSessionUuid) {
                displayResult('duplicate-analysis', { error: '请先创建会话' }, 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/sessions/${currentSessionUuid}/messages`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    const messages = data.data;
                    const analysis = analyzeMessageDuplicates(messages);
                    
                    testResults.totalMessages = analysis.totalMessages;
                    testResults.uniqueMessages = analysis.uniqueMessages;
                    testResults.hasDuplicates = analysis.hasDuplicates;
                    
                    displayResult('duplicate-analysis', analysis, analysis.hasDuplicates ? 'warning' : 'success');
                    displayMessages(messages, analysis.duplicateGroups);
                } else {
                    displayResult('duplicate-analysis', data, 'error');
                }
            } catch (error) {
                displayResult('duplicate-analysis', { error: error.message }, 'error');
            } finally {
                updateSummary();
            }
        }

        function analyzeMessageDuplicates(messages) {
            const messageGroups = new Map();
            const duplicateGroups = [];
            
            messages.forEach((message, index) => {
                const key = `${message.role}-${message.content.trim()}`;
                
                if (!messageGroups.has(key)) {
                    messageGroups.set(key, []);
                }
                messageGroups.get(key).push({ ...message, originalIndex: index });
            });
            
            messageGroups.forEach((group, key) => {
                if (group.length > 1) {
                    duplicateGroups.push({
                        key,
                        count: group.length,
                        messages: group
                    });
                }
            });
            
            return {
                totalMessages: messages.length,
                uniqueMessages: messageGroups.size,
                hasDuplicates: duplicateGroups.length > 0,
                duplicateGroups,
                duplicateCount: duplicateGroups.reduce((sum, group) => sum + group.count - 1, 0)
            };
        }

        function displayMessages(messages, duplicateGroups) {
            const container = document.getElementById('messages-display');
            
            if (!messages || messages.length === 0) {
                container.innerHTML = '<p>没有找到消息</p>';
                return;
            }

            const duplicateIndexes = new Set();
            duplicateGroups.forEach(group => {
                group.messages.forEach(msg => {
                    duplicateIndexes.add(msg.originalIndex);
                });
            });

            container.innerHTML = messages.map((message, index) => {
                const isDuplicate = duplicateIndexes.has(index);
                const cssClass = `message-item ${message.role}-message ${isDuplicate ? 'duplicate' : ''}`;
                
                return `
                    <div class="${cssClass}">
                        <strong>${message.role} ${isDuplicate ? '(🚨重复)' : ''}:</strong><br>
                        ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}<br>
                        <small>时间: ${new Date(message.created_at).toLocaleString()}</small>
                    </div>
                `;
            }).join('');
        }

        // 页面加载时生成随机邮箱
        window.onload = function() {
            generateRandomEmail();
            updateSummary();
        };
    </script>
</body>
</html>
