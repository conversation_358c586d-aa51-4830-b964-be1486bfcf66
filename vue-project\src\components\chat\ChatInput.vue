<template>
  <div class="chat-input">
    <div class="input-container">
      <div class="input-wrapper">
        <textarea
          ref="textareaRef"
          v-model="inputText"
          class="message-input"
          :class="{ 'input-disabled': disabled }"
          placeholder="输入消息..."
          rows="1"
          :disabled="disabled"
          @keydown="handleKeydown"
          @input="handleInput"
          @paste="handlePaste"
        ></textarea>
        
        <div class="input-actions">
          <button
            class="btn-send"
            :class="{ 'btn-sending': loading }"
            :disabled="!canSend"
            @click="handleSend"
          >
            <span v-if="loading" class="loading-spinner">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
            <span v-else class="send-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 2L11 13M22 2L15 22L11 13M22 2L2 9L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
          </button>
        </div>
      </div>
      
      <div class="input-footer">
        <div class="input-hints">
          <span class="hint-text">按 Enter 发送，Shift + Enter 换行</span>
        </div>
        <div class="character-count" :class="{ 'count-warning': isNearLimit, 'count-error': isOverLimit }">
          {{ inputText.length }}/{{ maxLength }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'

// 定义props
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: 2000
  }
})

// 定义事件
const emit = defineEmits(['send-message'])

// 响应式数据
const inputText = ref('')
const textareaRef = ref(null)

// 计算属性
const canSend = computed(() => {
  return !props.disabled && 
         !props.loading && 
         inputText.value.trim().length > 0 && 
         inputText.value.length <= props.maxLength
})

const isNearLimit = computed(() => {
  return inputText.value.length > props.maxLength * 0.8
})

const isOverLimit = computed(() => {
  return inputText.value.length > props.maxLength
})

// 自动调整textarea高度
function adjustTextareaHeight() {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
      const scrollHeight = textareaRef.value.scrollHeight
      const maxHeight = 120 // 最大高度约5行
      textareaRef.value.style.height = Math.min(scrollHeight, maxHeight) + 'px'
    }
  })
}

// 处理输入
function handleInput() {
  adjustTextareaHeight()
}

// 处理键盘事件
function handleKeydown(event) {
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      // Shift + Enter 换行，不做处理
      return
    } else {
      // Enter 发送消息
      event.preventDefault()
      handleSend()
    }
  }
}

// 处理粘贴
function handlePaste(event) {
  // 延迟调整高度，等待粘贴内容插入
  setTimeout(() => {
    adjustTextareaHeight()
  }, 0)
}

// 发送消息
function handleSend() {
  if (!canSend.value) return

  const message = inputText.value.trim()
  if (message) {
    emit('send-message', message)
    inputText.value = ''
    adjustTextareaHeight()
    
    // 重新聚焦输入框
    nextTick(() => {
      if (textareaRef.value) {
        textareaRef.value.focus()
      }
    })
  }
}

// 监听loading状态变化，完成后聚焦输入框
watch(() => props.loading, (newLoading, oldLoading) => {
  if (oldLoading && !newLoading) {
    nextTick(() => {
      if (textareaRef.value) {
        textareaRef.value.focus()
      }
    })
  }
})

// 组件挂载后聚焦输入框
import { onMounted } from 'vue'
onMounted(() => {
  if (textareaRef.value) {
    textareaRef.value.focus()
  }
})
</script>

<style scoped>
.chat-input {
  max-width: 800px;
  margin: 0 auto;
}

.input-container {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-glass);
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--space-md);
}

.message-input {
  flex: 1;
  background: var(--color-background-dark);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-md);
  padding: var(--space-md);
  color: var(--color-text-primary);
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  min-height: 44px;
  max-height: 120px;
}

.message-input:focus {
  border-color: var(--color-accent-purple);
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.message-input::placeholder {
  color: var(--color-text-muted);
}

.message-input.input-disabled {
  background: var(--color-surface-primary);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.btn-send {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
  border: none;
  width: 44px;
  height: 44px;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn-send:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-yellow);
}

.btn-send:active:not(:disabled) {
  transform: scale(0.95);
}

.btn-send:disabled {
  background: var(--color-surface-secondary);
  color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-sending {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.send-icon {
  transition: transform 0.2s ease;
}

.btn-send:hover:not(:disabled) .send-icon {
  transform: translateX(2px);
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-md);
  padding-top: var(--space-sm);
  border-top: 1px solid var(--color-border-glass);
}

.input-hints {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.hint-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.character-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  font-variant-numeric: tabular-nums;
}

.count-warning {
  color: var(--color-warning);
}

.count-error {
  color: var(--color-error);
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-container {
    padding: var(--space-md);
  }
  
  .input-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }
  
  .hint-text {
    display: none;
  }
}

/* 滚动条样式 */
.message-input::-webkit-scrollbar {
  width: 6px;
}

.message-input::-webkit-scrollbar-track {
  background: var(--color-surface-primary);
  border-radius: 3px;
}

.message-input::-webkit-scrollbar-thumb {
  background: var(--color-surface-secondary);
  border-radius: 3px;
}

.message-input::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}
</style>
