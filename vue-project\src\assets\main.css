/* 设计令牌 (Design Tokens) */
:root {
  /* 颜色系统 */
  --color-background-dark: #121212;
  --color-background-darker: #1A1A1A;
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #E0E0E0;
  --color-text-tertiary: #B0B0B0;
  --color-text-muted: #808080;

  --color-surface-primary: #3A3A3A;
  --color-surface-secondary: #4F4F4F;
  --color-surface-glass: rgba(255, 255, 255, 0.1);
  --color-border-glass: rgba(255, 255, 255, 0.2);

  --color-accent-yellow: #FFD60A;
  --color-accent-purple: #9B59B6;
  --color-accent-purple-light: #BE93FD;

  --color-success: #4CAF50;
  --color-error: #F44336;
  --color-warning: #FF9800;

  /* 字体系统 */
  --font-family-base: 'PingFang SC', 'Noto Sans SC', 'Inter', 'SF Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-2xl: 32px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;

  --line-height-tight: 1.3;
  --line-height-normal: 1.6;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;

  /* 圆角 */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;

  /* 阴影 */
  --shadow-glow-purple: 0 0 12px rgba(155, 89, 182, 0.5);
  --shadow-glow-yellow: 0 0 8px rgba(255, 214, 10, 0.3);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Z-index */
  --z-index-modal: 1000;
  --z-index-navbar: 900;
  --z-index-dropdown: 800;

  /* 断点 */
  --breakpoint-mobile: 375px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;

  /* 内容宽度 */
  --content-max-width: 768px;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
  background: linear-gradient(135deg, var(--color-background-dark) 0%, var(--color-background-darker) 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* 玻璃拟态效果 */
.glass {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  box-shadow: var(--shadow-glass);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  outline: none;
}

.btn:focus-visible {
  outline: 2px solid var(--color-accent-purple);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-yellow);
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-secondary {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-surface-secondary);
}

.btn-secondary:hover {
  background: var(--color-surface-primary);
  transform: translateY(-2px);
}

.btn:disabled {
  background: var(--color-surface-secondary);
  color: var(--color-text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: var(--space-md);
  background: var(--color-background-dark);
  border: 1px solid var(--color-surface-secondary);
  border-radius: var(--border-radius-sm);
  color: var(--color-text-primary);
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.input:focus {
  border-color: var(--color-accent-purple);
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.input::placeholder {
  color: var(--color-text-muted);
}

/* 卡片样式 */
.card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-glass);
}

/* 文本样式 */
.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

/* 布局工具类 */
.container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--space-sm);
}

.gap-md {
  gap: var(--space-md);
}

.gap-lg {
  gap: var(--space-lg);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-xl);
  }
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-dots {
  display: inline-flex;
  gap: var(--space-xs);
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: var(--color-accent-purple);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}
