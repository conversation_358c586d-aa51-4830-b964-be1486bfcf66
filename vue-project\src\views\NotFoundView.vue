<template>
  <div class="not-found-view">
    <div class="container">
      <div class="not-found-content">
        <div class="not-found-visual">
          <div class="error-code">404</div>
          <div class="floating-stars">
            <div class="star star-1"></div>
            <div class="star star-2"></div>
            <div class="star star-3"></div>
          </div>
        </div>
        
        <div class="not-found-text">
          <h1 class="error-title">页面走丢了</h1>
          <p class="error-description">
            抱歉，您访问的页面不存在。可能是链接有误，或者页面已被移动。
          </p>
          
          <div class="error-actions">
            <router-link to="/" class="btn btn-primary">
              返回首页
            </router-link>
            <button class="btn btn-secondary" @click="goBack">
              返回上页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function goBack() {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl) 0;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
}

.not-found-visual {
  position: relative;
  margin-bottom: var(--space-3xl);
}

.error-code {
  font-size: clamp(4rem, 15vw, 8rem);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-purple);
  opacity: 0.8;
  letter-spacing: -0.05em;
  margin-bottom: var(--space-lg);
}

.floating-stars {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.star {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--color-accent-purple-light);
  border-radius: 50%;
  animation: twinkle 2s ease-in-out infinite;
}

.star-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.star-2 {
  top: 40%;
  right: 30%;
  animation-delay: 0.7s;
}

.star-3 {
  bottom: 30%;
  left: 40%;
  animation-delay: 1.4s;
}

.error-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  letter-spacing: -0.02em;
}

.error-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-xl);
}

.error-actions {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  flex-wrap: wrap;
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
