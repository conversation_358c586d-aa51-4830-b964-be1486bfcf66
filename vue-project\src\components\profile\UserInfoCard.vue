<template>
  <div class="user-info-card">
    <div class="card-header">
      <div class="header-content">
        <div class="header-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="card-title">个人信息</h2>
          <p class="card-subtitle">查看您的账户详情</p>
        </div>
      </div>
    </div>

    <div class="card-content">
      <div v-if="userStore.userInfo" class="user-details">
        <!-- 基本信息 -->
        <div class="info-section">
          <div class="info-item">
            <span class="info-label">邮箱地址</span>
            <span class="info-value">
              {{ userStore.userInfo.email || '游客用户' }}
            </span>
          </div>

          <div class="info-item">
            <span class="info-label">账户类型</span>
            <span class="info-value">
              <span class="user-type-badge" :class="{ 'guest': userStore.isGuest }">
                {{ userStore.userType }}
              </span>
            </span>
          </div>

          <div class="info-item">
            <span class="info-label">账户状态</span>
            <span class="info-value">
              <span class="status-badge" :class="userStore.userInfo.status">
                {{ userStore.accountStatus }}
              </span>
            </span>
          </div>

          <div class="info-item">
            <span class="info-label">注册时间</span>
            <span class="info-value">
              {{ formatTime(userStore.userInfo.created_at) }}
            </span>
          </div>
        </div>

        <!-- 使用统计 -->
        <div class="info-section">
          <h3 class="section-title">使用统计</h3>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ userStore.userInfo.daily_message_count || 0 }}</div>
              <div class="stat-label">今日消息数</div>
            </div>
            
            <div class="stat-item">
              <div class="stat-value">{{ userStore.userInfo.message_credits || 0 }}</div>
              <div class="stat-label">剩余点数</div>
            </div>
          </div>
        </div>

        <!-- 用户画像 -->
        <div v-if="userStore.userInfo.profile_summary" class="info-section">
          <h3 class="section-title">AI分析画像</h3>
          <div class="profile-summary">
            <p>{{ userStore.userInfo.profile_summary }}</p>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="userStore.isLoading" class="loading-content">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <p>正在加载用户信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-content">
        <p>暂无用户信息</p>
        <button class="btn btn-secondary btn-sm" @click="userStore.loadUserInfo()">
          重新加载
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'

// 状态管理
const userStore = useUserStore()

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '未知'
  
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.user-info-card {
  background: var(--color-surface-glass);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid var(--color-border-glass);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all 0.3s ease;
}

.user-info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glass), var(--shadow-glow-purple);
}

/* 卡片头部 */
.card-header {
  background: var(--color-surface-primary);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--color-border-glass);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: var(--color-accent-purple);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 卡片内容 */
.card-content {
  padding: var(--space-lg);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.section-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  padding-bottom: var(--space-sm);
  border-bottom: 1px solid var(--color-border-glass);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  text-align: right;
}

.user-type-badge {
  background: var(--color-accent-purple);
  color: var(--color-text-primary);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.user-type-badge.guest {
  background: var(--color-accent-yellow);
  color: var(--color-background-dark);
}

.status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.status-badge.banned {
  background: rgba(244, 67, 54, 0.2);
  color: var(--color-error);
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
}

.stat-item {
  background: var(--color-surface-secondary);
  padding: var(--space-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--color-surface-primary);
  transform: translateY(-1px);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-yellow);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 用户画像 */
.profile-summary {
  background: var(--color-surface-secondary);
  padding: var(--space-lg);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--color-accent-purple);
}

.profile-summary p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
  font-style: italic;
}

/* 加载和错误状态 */
.loading-content, .error-content {
  text-align: center;
  padding: var(--space-xl);
  color: var(--color-text-secondary);
}

.loading-content p, .error-content p {
  margin-top: var(--space-md);
  font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .info-value {
    text-align: left;
  }
}
</style>
